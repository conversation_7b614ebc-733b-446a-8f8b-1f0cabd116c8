# 📝 套利信息提取配置修改指南

## 🎯 快速修改指南

### 1. 修改字数限制
**文件位置：** `配置文件/套利信息提取规则.yaml`
**修改位置：** `文章生成限制 > 字数控制`

```yaml
文章生成限制:
  字数控制:
    最小字数: 800      # 修改最小字数
    最大字数: 2500     # 修改最大字数
    警告阈值: 2000     # 修改警告阈值
```

### 2. 修改风险提示内容
**修改位置：** `风险提示 > 标准风险提示`

```yaml
风险提示:
  标准风险提示:
    - "投资有风险，入市需谨慎"
    - "套利策略存在失效风险"
    - "市场变化可能影响预期收益"
    - "建议根据个人风险承受能力操作"
    # 可以添加更多风险提示
    - "您的自定义风险提示"
```

### 3. 添加禁止表述
**修改位置：** `禁止内容 > 禁止表述`

```yaml
禁止内容:
  禁止表述:
    - "保证盈利"
    - "无风险套利"
    - "100%成功"
    - "稳赚不赔"
    - "必然获利"
    # 添加您要禁止的表述
    - "您的禁止表述"
```

### 4. 修改报告结构
**修改位置：** `报告结构 > 内容要求`

```yaml
报告结构:
  内容要求:
    摘要部分:
      字数限制: 200    # 修改摘要字数限制
      必须包含: ["套利类型", "预期收益", "风险等级"]
      
    详细分析:
      字数限制: 1200   # 修改详细分析字数限制
      必须包含: ["市场分析", "标的分析", "策略分析"]
```

### 5. 自定义关键词
**修改位置：** `自定义规则 > 用户自定义`

```yaml
自定义规则:
  用户自定义:
    特殊关键词: ["可转债", "分红", "价差"]  # 添加特别关注的关键词
    排除关键词: ["广告", "推广"]           # 添加需要排除的关键词
    重点标的: ["000001", "000002"]        # 添加重点关注的标的代码
```

### 6. 修改个性化设置
**修改位置：** `自定义规则 > 个性化设置`

```yaml
自定义规则:
  个性化设置:
    分析深度偏好: "深入"    # 简单/中等/深入
    风险偏好: "平衡"        # 激进/平衡/保守
    信息详细程度: "详细"    # 简洁/适中/详细
```

## 🔧 高级配置修改

### 1. 修改AI提示词模板
**修改位置：** `AI提示词模板 > 基础提取提示 > 模板`

您可以完全自定义AI提取信息时使用的提示词模板。模板中可以使用以下变量：
- `{标题}` - 文章标题
- `{作者}` - 文章作者
- `{内容}` - 文章内容
- `{最小字数}` - 最小字数限制
- `{最大字数}` - 最大字数限制
- `{禁止表述}` - 禁止表述列表

### 2. 修改数据验证规则
**修改位置：** `数据验证规则`

```yaml
数据验证规则:
  必需字段:
    - 套利标的
    - 操作策略
    - 风险评估
    # 添加您认为必需的字段
    
  格式验证:
    价格数据: "必须为数字格式"
    日期数据: "必须为YYYY-MM-DD格式"
    # 添加更多格式验证规则
```

### 3. 修改输出格式
**修改位置：** `输出格式`

```yaml
输出格式:
  文件命名:
    前缀: "套利分析报告"     # 修改文件名前缀
    时间戳: true            # 是否包含时间戳
    扩展名: ".md"           # 文件扩展名
    
  特殊标记:
    重要信息: "**粗体**"
    风险提示: "⚠️ 风险提示"
    操作建议: "💡 操作建议"
    数据信息: "📊 数据信息"
```

## 📋 常用配置场景

### 场景1：生成更简洁的报告
```yaml
文章生成限制:
  字数控制:
    最小字数: 500
    最大字数: 1500
    
报告结构:
  内容要求:
    摘要部分:
      字数限制: 150
    详细分析:
      字数限制: 800
```

### 场景2：生成更详细的报告
```yaml
文章生成限制:
  字数控制:
    最小字数: 1200
    最大字数: 4000
    
  内容深度:
    分析层次: "深入"
    技术术语使用: "大量"
```

### 场景3：更严格的风险控制
```yaml
风险提示:
  标准风险提示:
    - "投资有风险，入市需谨慎"
    - "套利策略存在失效风险"
    - "市场变化可能影响预期收益"
    - "建议根据个人风险承受能力操作"
    - "本策略仅供参考，不构成投资建议"
    - "请充分了解相关风险后再进行操作"
    
禁止内容:
  禁止表述:
    - "保证盈利"
    - "无风险套利"
    - "100%成功"
    - "稳赚不赔"
    - "必然获利"
    - "零风险"
    - "绝对安全"
```

## 🔄 配置生效方法

### 方法1：重新运行程序
修改配置文件后，重新运行套利信息提取程序，新配置会自动生效。

### 方法2：动态重新加载
```python
from 套利信息提取配置 import 套利配置

# 重新加载配置
套利配置.加载配置()
```

### 方法3：程序内修改
```python
from 套利信息提取配置 import 套利配置

# 修改配置
套利配置.配置数据['文章生成限制']['字数控制']['最大字数'] = 3000

# 保存配置
套利配置.保存配置()
```

## ⚠️ 注意事项

### 1. 备份配置文件
在修改配置前，建议备份原配置文件：
```bash
copy "配置文件\套利信息提取规则.yaml" "配置文件\套利信息提取规则_备份.yaml"
```

### 2. YAML语法检查
修改后可以使用以下命令检查YAML语法是否正确：
```python
python -c "import yaml; yaml.safe_load(open('配置文件/套利信息提取规则.yaml', 'r', encoding='utf-8')); print('✅ YAML语法正确')"
```

### 3. 配置验证
运行配置验证程序检查配置完整性：
```python
python "配置文件\套利信息提取配置.py"
```

### 4. 常见错误
- **YAML缩进错误**：确保使用空格而不是Tab，保持一致的缩进
- **引号问题**：包含特殊字符的字符串需要用引号包围
- **列表格式**：列表项前必须有`-`符号和空格

## 🎯 配置优化建议

### 1. 根据使用场景调整
- **日常分析**：使用中等深度，平衡的风险偏好
- **专业报告**：使用深入分析，详细的信息程度
- **快速浏览**：使用简单分析，简洁的信息程度

### 2. 定期更新禁止词汇
根据实际使用情况，定期更新禁止表述和限制词汇列表。

### 3. 自定义关键词维护
根据市场变化和关注重点，及时更新特殊关键词和重点标的列表。

## 📞 技术支持

如果在配置修改过程中遇到问题，可以：
1. 查看配置文件语法是否正确
2. 运行配置验证程序
3. 查看程序运行日志
4. 恢复备份配置文件

---

**记住：所有配置修改都会立即影响套利信息提取的结果，请谨慎修改并及时测试！**
