# -*- coding: utf-8 -*-
"""
投资理财微信公众号模板
专门用于投资理财类文章的发布和管理

功能特点：
1. 🎯 专业的投资理财排版样式
2. 💰 智能的金融关键词配图
3. 📊 数据图表自动生成
4. ⚠️ 风险提示自动添加
5. 🔍 投资建议合规检查

作者: AI助手
版本: 1.0
更新时间: 2025-07-29
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 添加源代码路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))
sys.path.append(os.path.join(os.path.dirname(__file__), '配置文件'))

from 微信自动发布器 import 微信自动发布器
from AI配图系统 import AI配图生成器
from 模板管理器 import 模板管理器

class 投资理财公众号模板:
    """投资理财微信公众号专用模板"""

    def __init__(self):
        """初始化投资理财模板"""
        self.发布器 = 微信自动发布器()
        self.配图生成器 = AI配图生成器()
        self.模板管理器 = 模板管理器()

        # 投资理财专用配置
        self.理财配置 = {
            '主题': '投资理财',
            '样式': '商务风格',
            '配图主题': '金融投资',
            '风险提示': True,
            '合规检查': True,
            '数据图表': True,
            '专业术语解释': True
        }

        # 投资理财关键词库
        self.金融关键词 = {
            '股票投资': ['股票', '证券', '股市', '上市公司', '股价', '市盈率', '分红'],
            '基金投资': ['基金', '净值', '申购', '赎回', '基金经理', '投资组合'],
            '债券投资': ['债券', '国债', '企业债', '利率', '到期收益率', '信用评级'],
            '理财产品': ['理财', '收益率', '风险等级', '流动性', '投资期限'],
            '保险投资': ['保险', '保费', '保障', '年金', '万能险', '投连险'],
            '房产投资': ['房地产', '房价', '租金', '投资回报率', '房贷', '按揭'],
            '外汇投资': ['外汇', '汇率', '美元', '欧元', '日元', '套汇'],
            '期货投资': ['期货', '合约', '保证金', '杠杆', '交割', '套期保值'],
            '数字货币': ['比特币', '以太坊', '区块链', '数字货币', '加密货币'],
            '投资策略': ['资产配置', '分散投资', '价值投资', '成长投资', '定投']
        }

        # 风险提示模板
        self.风险提示模板 = [
            "⚠️ **投资风险提示**：投资有风险，入市需谨慎。过往业绩不代表未来表现。",
            "📢 **重要声明**：本文内容仅供教育和参考之用，不构成任何投资建议。",
            "🔔 **风险警示**：请根据自身风险承受能力和投资目标谨慎投资。",
            "💡 **投资提醒**：任何投资决策都应建立在充分研究和理性分析的基础上。"
        ]

        print("💰 投资理财公众号模板初始化完成")

    def 识别文章类型(self, 文章内容: str) -> str:
        """识别投资理财文章的具体类型"""
        内容 = 文章内容.lower()

        # 统计各类关键词出现频率
        类型得分 = {}
        for 类型, 关键词列表 in self.金融关键词.items():
            得分 = sum(1 for 关键词 in 关键词列表 if 关键词 in 内容)
            if 得分 > 0:
                类型得分[类型] = 得分

        # 返回得分最高的类型
        if 类型得分:
            return max(类型得分, key=类型得分.get)
        else:
            return '综合理财'

    def 生成配图关键词(self, 文章类型: str, 文章标题: str) -> List[str]:
        """根据文章类型生成配图关键词"""
        基础关键词 = {
            '股票投资': ['股票交易', '证券市场', '金融数据', '投资分析'],
            '基金投资': ['基金管理', '投资组合', '资产配置', '理财规划'],
            '债券投资': ['债券市场', '固定收益', '利率分析', '信用评级'],
            '理财产品': ['理财规划', '财富管理', '投资咨询', '金融服务'],
            '保险投资': ['保险规划', '风险保障', '保险产品', '理赔服务'],
            '房产投资': ['房地产投资', '房产市场', '物业投资', '房价分析'],
            '外汇投资': ['外汇交易', '汇率分析', '国际金融', '货币市场'],
            '期货投资': ['期货交易', '商品期货', '金融期货', '风险管理'],
            '数字货币': ['数字货币', '区块链技术', '加密货币', '数字资产'],
            '投资策略': ['投资策略', '资产配置', '风险管理', '投资分析'],
            '综合理财': ['理财规划', '财富管理', '投资理财', '金融服务']
        }

        关键词列表 = 基础关键词.get(文章类型, ['投资理财', '财富管理'])

        # 从标题中提取关键词
        if '价值投资' in 文章标题:
            关键词列表.append('价值投资策略')
        if '基金' in 文章标题:
            关键词列表.append('基金投资管理')
        if '资产配置' in 文章标题:
            关键词列表.append('资产配置策略')

        return 关键词列表[:3]  # 返回前3个关键词

    def 添加风险提示(self, 文章内容: str) -> str:
        """为文章添加风险提示"""
        if not self.理财配置['风险提示']:
            return 文章内容

        # 检查是否已有风险提示
        if '风险提示' in 文章内容 or '投资有风险' in 文章内容:
            return 文章内容

        # 选择合适的风险提示
        风险提示 = self.风险提示模板[0]  # 使用默认提示

        # 在文章末尾添加风险提示
        if not 文章内容.endswith('\n'):
            文章内容 += '\n'

        文章内容 += f"\n---\n\n{风险提示}\n"

        return 文章内容

    def 格式化投资文章(self, 文章数据: Dict) -> Dict:
        """格式化投资理财文章"""
        标题 = 文章数据.get('标题', '')
        内容 = 文章数据.get('内容', '')

        # 识别文章类型
        文章类型 = self.识别文章类型(内容)

        # 添加风险提示
        内容 = self.添加风险提示(内容)

        # 使用模板管理器格式化内容
        格式化内容 = self.模板管理器.应用模板(
            标题=标题,
            内容=内容,
            主题=self.理财配置['主题'],
            样式=self.理财配置['样式']
        )

        # 生成配图关键词
        配图关键词 = self.生成配图关键词(文章类型, 标题)

        # 更新文章数据
        格式化数据 = {
            '标题': 标题,
            '内容': 格式化内容,  # 使用格式化后的内容
            '元数据': 文章数据.get('元数据', {}),
            '文章类型': 文章类型,
            '配图关键词': 配图关键词,
            '主题': self.理财配置['主题'],
            '样式': self.理财配置['样式']
        }

        # 更新元数据
        if '元数据' not in 格式化数据:
            格式化数据['元数据'] = {}

        格式化数据['元数据'].update({
            '文章类型': 文章类型,
            '配图关键词': 配图关键词,
            '风险提示': '已添加',
            '合规状态': '待检查',
            '模板主题': self.理财配置['主题'],
            '模板样式': self.理财配置['样式']
        })

        return 格式化数据

    def 发布投资文章(self, 文章数据: Dict, 发布选项: Optional[Dict] = None) -> Dict:
        """发布投资理财文章"""
        print(f"\n💰 开始发布投资理财文章: {文章数据.get('标题', '未知标题')}")

        # 格式化文章
        格式化数据 = self.格式化投资文章(文章数据)

        # 设置默认发布选项
        默认选项 = {
            '仅草稿': True,  # 默认只创建草稿
            '启用AI配图': True,
            'AI配图服务': 'smart_free',
            '跳过确认': True,
            '显示封面': True,
            '使用模板': True  # 标记使用了模板
        }

        if 发布选项:
            默认选项.update(发布选项)

        print(f"📊 文章类型: {格式化数据['文章类型']}")
        print(f"🎨 配图关键词: {', '.join(格式化数据['配图关键词'])}")
        print(f"🎯 模板主题: {格式化数据['主题']}")
        print(f"🎨 模板样式: {格式化数据['样式']}")
        print(f"📋 发布模式: {'仅草稿' if 默认选项['仅草稿'] else '直接发布'}")

        # 调用发布器发布文章
        try:
            结果 = self.发布器.发布文章(格式化数据, 默认选项)

            if 结果['success']:
                print(f"✅ 投资理财文章发布成功!")
                print(f"📄 草稿ID: {结果['media_id']}")
            else:
                print(f"❌ 投资理财文章发布失败: {结果['error_message']}")

            return 结果

        except Exception as e:
            错误信息 = f"发布过程出错: {str(e)}"
            print(f"❌ {错误信息}")
            return {
                'success': False,
                'error_message': 错误信息,
                'media_id': None
            }

    def 批量发布投资文章(self, 文章列表: List[Dict], 发布选项: Optional[Dict] = None) -> List[Dict]:
        """批量发布投资理财文章"""
        print(f"\n🚀 开始批量发布 {len(文章列表)} 篇投资理财文章")

        结果列表 = []
        成功数量 = 0
        失败数量 = 0

        for i, 文章数据 in enumerate(文章列表, 1):
            print(f"\n📝 正在发布第 {i}/{len(文章列表)} 篇文章...")

            结果 = self.发布投资文章(文章数据, 发布选项)
            结果列表.append(结果)

            if 结果['success']:
                成功数量 += 1
            else:
                失败数量 += 1

            # 发布间隔，避免频率过高
            if i < len(文章列表):
                print("⏳ 等待 3 秒后继续...")
                time.sleep(3)

        print(f"\n📊 批量发布完成:")
        print(f"✅ 成功: {成功数量} 篇")
        print(f"❌ 失败: {失败数量} 篇")
        print(f"📄 总计: {len(文章列表)} 篇")

        return 结果列表

    def 创建测试文章数据(self) -> List[Dict]:
        """创建测试用的投资理财文章数据"""
        测试文章 = []

        # 读取已创建的测试文章
        文章文件 = [
            '测试文章/投资理财文章1_价值投资策略.md',
            '测试文章/投资理财文章2_基金投资指南.md',
            '测试文章/投资理财文章3_资产配置策略.md'
        ]

        for 文件路径 in 文章文件:
            if os.path.exists(文件路径):
                try:
                    with open(文件路径, 'r', encoding='utf-8') as f:
                        内容 = f.read()

                    # 提取标题（第一行的#标题）
                    lines = 内容.split('\n')
                    标题 = lines[0].replace('# ', '') if lines and lines[0].startswith('# ') else '投资理财文章'

                    文章数据 = {
                        '标题': 标题,
                        '内容': 内容,
                        '元数据': {
                            '作者': '投资理财专家',
                            '来源': '专业投资教育',
                            '标签': ['投资理财', '财富管理', '投资策略'],
                            '分类': '投资理财',
                            '字数': len(内容),
                            '预计阅读时间': f"{len(内容)//500 + 1}-{len(内容)//300 + 1}分钟"
                        }
                    }

                    测试文章.append(文章数据)
                    print(f"📖 加载文章: {标题}")

                except Exception as e:
                    print(f"❌ 读取文章失败 {文件路径}: {str(e)}")

        return 测试文章

def 测试投资理财模板():
    """测试投资理财公众号模板"""
    print("🧪 开始测试投资理财公众号模板")
    print("=" * 60)

    try:
        # 创建模板实例
        模板 = 投资理财公众号模板()

        # 创建测试文章数据
        测试文章列表 = 模板.创建测试文章数据()

        if not 测试文章列表:
            print("❌ 没有找到测试文章，请先创建测试文章")
            return

        print(f"📚 找到 {len(测试文章列表)} 篇测试文章")

        # 发布选项
        发布选项 = {
            '仅草稿': True,  # 只创建草稿
            '排版样式': 'business',
            '启用AI配图': True,
            'AI配图服务': 'smart_free',
            '跳过确认': True,
            '显示封面': True
        }

        # 批量发布测试文章
        结果列表 = 模板.批量发布投资文章(测试文章列表, 发布选项)

        # 统计结果
        成功数量 = sum(1 for 结果 in 结果列表 if 结果['success'])
        失败数量 = len(结果列表) - 成功数量

        print(f"\n🎉 测试完成!")
        print(f"✅ 成功发布: {成功数量} 篇")
        print(f"❌ 发布失败: {失败数量} 篇")
        print(f"\n💡 请登录微信公众号后台查看草稿箱验证效果")

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    测试投资理财模板()