# -*- coding: utf-8 -*-
"""
通用模板管理器
支持多主题、多样式的文章模板管理

功能特点：
1. 🎨 多主题模板支持（投资、生活、科技等）
2. 📝 灵活的模板变量替换
3. 🎯 智能样式选择
4. 📊 模板效果统计
5. 🔧 易于扩展新主题

作者: AI助手
版本: 1.0
更新时间: 2025-07-29
"""

import os
import re
import json
from datetime import datetime
from typing import Dict, List, Optional, Any

class 模板管理器:
    """通用模板管理器"""
    
    def __init__(self):
        """初始化模板管理器"""
        self.模板库 = {}
        self.主题配置 = {}
        self._初始化默认模板()
        print("🎨 模板管理器初始化完成")
    
    def _初始化默认模板(self):
        """初始化默认模板"""
        
        # 投资理财主题模板
        self.模板库['投资理财'] = {
            '商务风格': {
                '标题样式': '<h1 style="color: #2c3e50; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0;">{title}</h1>',
                '内容模板': '''
<div style="font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.8; color: #333;">
    {title_section}
    
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="color: white; margin: 0; font-size: 16px;">
            📊 <strong>投资要点</strong>：{summary}
        </p>
    </div>
    
    {content_sections}
    
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
            ⚠️ <strong>风险提示</strong>：投资有风险，入市需谨慎。本文内容仅供参考，不构成投资建议。
        </p>
    </div>
    
    <div style="text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <p style="color: #6c757d; margin: 0;">💡 感谢您的阅读，欢迎分享和关注！</p>
    </div>
</div>
                ''',
                '段落样式': '<p style="margin: 15px 0; text-indent: 2em; line-height: 1.8;">{content}</p>',
                '标题样式_h2': '<h2 style="color: #34495e; font-size: 20px; font-weight: bold; margin: 25px 0 15px 0; border-left: 4px solid #3498db; padding-left: 15px;">{content}</h2>',
                '标题样式_h3': '<h3 style="color: #2c3e50; font-size: 18px; font-weight: bold; margin: 20px 0 10px 0;">{content}</h3>',
                '列表样式': '<ul style="margin: 15px 0; padding-left: 20px;">{items}</ul>',
                '列表项样式': '<li style="margin: 8px 0; line-height: 1.6;">{content}</li>',
                '强调样式': '<strong style="color: #e74c3c; font-weight: bold;">{content}</strong>',
                '引用样式': '<blockquote style="border-left: 4px solid #3498db; margin: 20px 0; padding: 15px 20px; background: #ecf0f1; font-style: italic;">{content}</blockquote>'
            },
            '简约风格': {
                '标题样式': '<h1 style="color: #333; font-size: 22px; text-align: center; margin: 20px 0; border-bottom: 2px solid #ddd; padding-bottom: 10px;">{title}</h1>',
                '内容模板': '''
<div style="font-family: 'Microsoft YaHei', sans-serif; line-height: 1.7; color: #444; max-width: 800px; margin: 0 auto;">
    {title_section}
    
    <div style="background: #f5f5f5; padding: 12px; border-radius: 5px; margin: 15px 0;">
        <p style="margin: 0; font-size: 15px; color: #666;">📝 {summary}</p>
    </div>
    
    {content_sections}
    
    <hr style="border: none; border-top: 1px solid #eee; margin: 25px 0;">
    
    <p style="text-align: center; color: #999; font-size: 14px;">
        ⚠️ 投资有风险，决策需谨慎
    </p>
</div>
                ''',
                '段落样式': '<p style="margin: 12px 0; line-height: 1.7;">{content}</p>',
                '标题样式_h2': '<h2 style="color: #555; font-size: 18px; margin: 20px 0 10px 0; font-weight: 600;">{content}</h2>',
                '标题样式_h3': '<h3 style="color: #666; font-size: 16px; margin: 15px 0 8px 0;">{content}</h3>'
            }
        }
        
        # 生活主题模板
        self.模板库['生活'] = {
            '温馨风格': {
                '标题样式': '<h1 style="color: #e91e63; font-size: 24px; text-align: center; margin: 20px 0; font-family: \'Microsoft YaHei\';">{title}</h1>',
                '内容模板': '''
<div style="font-family: 'Microsoft YaHei', sans-serif; line-height: 1.8; color: #555;">
    {title_section}
    
    <div style="background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%); padding: 15px; border-radius: 12px; margin: 20px 0;">
        <p style="color: #fff; margin: 0; font-size: 16px; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">
            🌸 <strong>生活感悟</strong>：{summary}
        </p>
    </div>
    
    {content_sections}
    
    <div style="text-align: center; margin: 25px 0; padding: 15px; background: #ffeaa7; border-radius: 10px;">
        <p style="color: #d63031; margin: 0;">💕 愿你的生活充满阳光与美好！</p>
    </div>
</div>
                ''',
                '段落样式': '<p style="margin: 15px 0; line-height: 1.8; text-indent: 2em;">{content}</p>',
                '标题样式_h2': '<h2 style="color: #e91e63; font-size: 20px; margin: 25px 0 15px 0; border-bottom: 2px solid #ffeaa7; padding-bottom: 8px;">{content}</h2>'
            }
        }
        
        # 科技主题模板
        self.模板库['科技'] = {
            '现代风格': {
                '标题样式': '<h1 style="color: #0984e3; font-size: 24px; text-align: center; margin: 20px 0; font-weight: 300; letter-spacing: 1px;">{title}</h1>',
                '内容模板': '''
<div style="font-family: 'Microsoft YaHei', 'Helvetica Neue', sans-serif; line-height: 1.7; color: #2d3436;">
    {title_section}
    
    <div style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: white; margin: 0; font-size: 15px;">
            🚀 <strong>科技前沿</strong>：{summary}
        </p>
    </div>
    
    {content_sections}
    
    <div style="border-top: 2px solid #74b9ff; padding-top: 15px; margin-top: 25px; text-align: center;">
        <p style="color: #636e72; margin: 0; font-size: 14px;">🔬 探索科技，创造未来</p>
    </div>
</div>
                ''',
                '段落样式': '<p style="margin: 12px 0; line-height: 1.7;">{content}</p>',
                '标题样式_h2': '<h2 style="color: #0984e3; font-size: 19px; margin: 22px 0 12px 0; font-weight: 500;">{content}</h2>'
            }
        }
        
        # 主题配置
        self.主题配置 = {
            '投资理财': {
                '默认样式': '商务风格',
                '关键词': ['投资', '理财', '股票', '基金', '债券', '保险', '房产', '金融'],
                '配图主题': ['商务', '金融', '数据'],
                '风险提示': True
            },
            '生活': {
                '默认样式': '温馨风格',
                '关键词': ['生活', '健康', '美食', '旅行', '家庭', '情感', '成长'],
                '配图主题': ['生活', '自然', '温馨'],
                '风险提示': False
            },
            '科技': {
                '默认样式': '现代风格',
                '关键词': ['科技', '技术', '互联网', '人工智能', 'AI', '编程', '创新'],
                '配图主题': ['科技', '现代', '创新'],
                '风险提示': False
            }
        }
    
    def 识别文章主题(self, 标题: str, 内容: str) -> str:
        """根据标题和内容识别文章主题"""
        文本 = (标题 + ' ' + 内容).lower()
        
        主题得分 = {}
        for 主题, 配置 in self.主题配置.items():
            得分 = 0
            for 关键词 in 配置['关键词']:
                得分 += 文本.count(关键词.lower()) * 2  # 关键词权重为2
            
            # 标题中的关键词权重更高
            for 关键词 in 配置['关键词']:
                if 关键词.lower() in 标题.lower():
                    得分 += 5
            
            主题得分[主题] = 得分
        
        # 返回得分最高的主题，如果没有明显主题则返回投资理财
        if max(主题得分.values()) > 0:
            return max(主题得分, key=主题得分.get)
        else:
            return '投资理财'  # 默认主题
    
    def 应用模板(self, 标题: str, 内容: str, 主题: Optional[str] = None, 样式: Optional[str] = None, **kwargs) -> str:
        """应用模板到文章内容"""
        
        # 自动识别主题
        if not 主题:
            主题 = self.识别文章主题(标题, 内容)
        
        # 选择样式
        if not 样式:
            样式 = self.主题配置.get(主题, {}).get('默认样式', '商务风格')
        
        # 获取模板
        模板 = self.模板库.get(主题, {}).get(样式)
        if not 模板:
            # 使用默认模板
            模板 = self.模板库['投资理财']['商务风格']
        
        # 生成摘要
        摘要 = kwargs.get('摘要', self._生成摘要(内容))
        
        # 处理内容
        处理后内容 = self._处理内容格式(内容, 模板)
        
        # 应用主模板
        标题部分 = 模板['标题样式'].format(title=标题)
        
        最终内容 = 模板['内容模板'].format(
            title_section=标题部分,
            summary=摘要,
            content_sections=处理后内容
        )
        
        print(f"✅ 已应用模板 - 主题: {主题}, 样式: {样式}")
        return 最终内容
    
    def _处理内容格式(self, 内容: str, 模板: Dict) -> str:
        """处理内容格式"""
        lines = 内容.split('\n')
        处理后行 = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 处理标题
            if line.startswith('## '):
                标题内容 = line[3:].strip()
                if '标题样式_h2' in 模板:
                    处理后行.append(模板['标题样式_h2'].format(content=标题内容))
                else:
                    处理后行.append(f'<h2>{标题内容}</h2>')
            elif line.startswith('### '):
                标题内容 = line[4:].strip()
                if '标题样式_h3' in 模板:
                    处理后行.append(模板['标题样式_h3'].format(content=标题内容))
                else:
                    处理后行.append(f'<h3>{标题内容}</h3>')
            elif line.startswith('# '):
                # 跳过一级标题，已在主模板中处理
                continue
            else:
                # 处理普通段落
                if line and not line.startswith('<'):
                    # 处理强调文本
                    line = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', line)
                    line = re.sub(r'\*(.*?)\*', r'<em>\1</em>', line)
                    
                    if '段落样式' in 模板:
                        处理后行.append(模板['段落样式'].format(content=line))
                    else:
                        处理后行.append(f'<p>{line}</p>')
                else:
                    处理后行.append(line)
        
        return '\n'.join(处理后行)
    
    def _生成摘要(self, 内容: str, 最大长度: int = 100) -> str:
        """生成文章摘要"""
        # 移除Markdown标记
        纯文本 = re.sub(r'[#*`\[\]()!]', '', 内容)
        纯文本 = re.sub(r'\n+', ' ', 纯文本)
        
        # 提取前几句话作为摘要
        句子列表 = re.split(r'[。！？]', 纯文本)
        摘要句子 = []
        摘要长度 = 0
        
        for 句子 in 句子列表:
            句子 = 句子.strip()
            if not 句子:
                continue
                
            if 摘要长度 + len(句子) <= 最大长度:
                摘要句子.append(句子)
                摘要长度 += len(句子)
            else:
                break
        
        摘要 = '。'.join(摘要句子)
        if 摘要 and not 摘要.endswith('。'):
            摘要 += '。'
        
        return 摘要 if 摘要 else "精彩内容，值得一读。"
    
    def 添加自定义模板(self, 主题: str, 样式: str, 模板配置: Dict):
        """添加自定义模板"""
        if 主题 not in self.模板库:
            self.模板库[主题] = {}
        
        self.模板库[主题][样式] = 模板配置
        print(f"✅ 已添加自定义模板: {主题} - {样式}")
    
    def 获取可用主题(self) -> List[str]:
        """获取所有可用主题"""
        return list(self.模板库.keys())
    
    def 获取主题样式(self, 主题: str) -> List[str]:
        """获取指定主题的所有样式"""
        return list(self.模板库.get(主题, {}).keys())

def 测试模板管理器():
    """测试模板管理器"""
    print("🧪 开始测试模板管理器")
    print("=" * 50)
    
    管理器 = 模板管理器()
    
    # 测试文章
    测试标题 = "价值投资策略深度解析"
    测试内容 = """
## 投资理念
价值投资是一种长期投资策略。

### 核心要点
- 寻找被低估的优质企业
- 长期持有
- 关注企业基本面

**重要提醒**：投资需要耐心和纪律。
    """
    
    # 测试主题识别
    识别主题 = 管理器.识别文章主题(测试标题, 测试内容)
    print(f"📊 识别主题: {识别主题}")
    
    # 应用模板
    格式化内容 = 管理器.应用模板(测试标题, 测试内容)
    
    print(f"✅ 模板应用成功")
    print(f"📝 内容长度: {len(格式化内容)} 字符")
    
    # 显示可用主题
    print(f"🎨 可用主题: {', '.join(管理器.获取可用主题())}")

if __name__ == "__main__":
    测试模板管理器()
