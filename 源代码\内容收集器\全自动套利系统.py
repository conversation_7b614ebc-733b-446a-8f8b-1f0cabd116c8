# -*- coding: utf-8 -*-
"""
全自动套利系统

作者: AI助手
日期: 2025-07-29
功能: 整合采集、分析、发布的完整自动化流程
"""

import os
import sys
import time
import threading
from datetime import datetime

# 添加当前目录到路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
sys.path.append(当前目录)

# 导入各个模块
from 半自动微信采集器 import 半自动微信采集器
from 套利信息提取器 import 套利信息提取器
from 公众号自动发布器 import 公众号发布器


class 全自动套利系统:
    """全自动套利分析和发布系统"""
    
    def __init__(self):
        """初始化系统"""
        self.采集器 = 半自动微信采集器(采集天数=2)
        self.分析器 = 套利信息提取器()
        self.发布器 = 公众号发布器()
        
        self.运行中 = False
        self.最后分析时间 = 0
        self.分析间隔 = 300  # 5分钟分析一次
        
        print("🤖 全自动套利系统初始化完成")
    
    def 启动监听模式(self):
        """启动监听模式，自动处理整个流程"""
        print("🎧 启动全自动监听模式...")
        print("💡 系统将自动：")
        print("   1. 监听剪贴板中的微信文章链接")
        print("   2. 自动采集和保存文章")
        print("   3. 定期分析套利信息")
        print("   4. 自动发布分析报告到公众号")
        print()
        
        self.运行中 = True
        
        # 启动采集线程
        采集线程 = threading.Thread(target=self._采集线程, daemon=True)
        采集线程.start()
        
        # 启动分析发布线程
        分析线程 = threading.Thread(target=self._分析发布线程, daemon=True)
        分析线程.start()
        
        try:
            print("🚀 系统已启动，按 Ctrl+C 停止")
            print("📋 现在可以在微信中复制文章链接了...")
            
            while self.运行中:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ 用户停止系统")
            self.运行中 = False
    
    def _采集线程(self):
        """采集线程"""
        try:
            # 使用采集器的监听功能，但设置较短的超时
            while self.运行中:
                print("👂 开始新一轮剪贴板监听...")
                文章列表 = self.采集器.监听剪贴板(超时秒数=60)
                
                if 文章列表:
                    print(f"✅ 本轮采集到 {len(文章列表)} 篇文章")
                
                # 短暂休息后继续监听
                time.sleep(2)
                
        except Exception as e:
            print(f"❌ 采集线程异常: {e}")
    
    def _分析发布线程(self):
        """分析和发布线程"""
        try:
            while self.运行中:
                当前时间 = time.time()
                
                # 检查是否需要进行分析
                if 当前时间 - self.最后分析时间 > self.分析间隔:
                    self._执行分析发布()
                    self.最后分析时间 = 当前时间
                
                # 每30秒检查一次
                time.sleep(30)
                
        except Exception as e:
            print(f"❌ 分析发布线程异常: {e}")
    
    def _执行分析发布(self):
        """执行分析和发布"""
        try:
            print("\n🔍 开始定期分析...")
            
            # 检查是否有新文章
            根目录 = os.path.dirname(os.path.dirname(当前目录))
            文章目录 = os.path.join(根目录, "采集结果", "微信文章")
            
            if not os.path.exists(文章目录):
                print("📁 暂无采集文章，跳过分析")
                return
            
            文章文件 = [f for f in os.listdir(文章目录) if f.endswith('.txt')]
            
            if not 文章文件:
                print("📄 暂无文章文件，跳过分析")
                return
            
            print(f"📊 发现 {len(文章文件)} 篇文章，开始分析...")
            
            # 执行套利分析
            套利信息列表 = self.分析器.批量提取(文章目录)
            
            if not 套利信息列表:
                print("❌ 分析结果为空，跳过发布")
                return
            
            # 生成分析报告
            报告内容 = self.分析器.生成套利报告(套利信息列表)
            
            # 保存报告
            采集结果目录 = os.path.join(根目录, "采集结果")
            报告文件 = os.path.join(采集结果目录, f"套利分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            
            with open(报告文件, 'w', encoding='utf-8') as f:
                f.write(报告内容)
            
            print(f"💾 分析报告已保存: {os.path.basename(报告文件)}")
            
            # 检查是否配置了公众号发布
            if self._检查发布配置():
                print("📢 开始自动发布到公众号...")
                
                发布成功 = self.发布器.自动发布套利报告(报告文件)
                
                if 发布成功:
                    print("🎉 报告已自动发布到公众号！")
                else:
                    print("❌ 自动发布失败，请检查配置")
            else:
                print("⚠️ 未配置公众号发布，跳过自动发布")
                print("💡 如需自动发布，请配置公众号AppID和AppSecret")
            
        except Exception as e:
            print(f"❌ 分析发布过程异常: {e}")
    
    def _检查发布配置(self) -> bool:
        """检查是否配置了公众号发布"""
        try:
            return (hasattr(self.发布器, 'app_id') and 
                   self.发布器.app_id and 
                   self.发布器.app_id != "你的公众号AppID")
        except:
            return False
    
    def 手动执行一次(self):
        """手动执行一次完整流程"""
        print("🔄 手动执行完整分析流程...")
        
        try:
            self._执行分析发布()
            print("✅ 手动执行完成")
        except Exception as e:
            print(f"❌ 手动执行失败: {e}")
    
    def 显示系统状态(self):
        """显示系统状态"""
        print("\n📊 系统状态报告")
        print("=" * 50)
        
        # 检查采集状态
        根目录 = os.path.dirname(os.path.dirname(当前目录))
        文章目录 = os.path.join(根目录, "采集结果", "微信文章")
        
        if os.path.exists(文章目录):
            文章数量 = len([f for f in os.listdir(文章目录) if f.endswith('.txt')])
            print(f"📄 已采集文章: {文章数量} 篇")
        else:
            print("📄 已采集文章: 0 篇")
        
        # 检查分析报告
        采集结果目录 = os.path.join(根目录, "采集结果")
        if os.path.exists(采集结果目录):
            报告文件 = [f for f in os.listdir(采集结果目录) if f.startswith("套利分析报告_")]
            print(f"📊 分析报告: {len(报告文件)} 份")
            
            if 报告文件:
                最新报告 = sorted(报告文件)[-1]
                print(f"📅 最新报告: {最新报告}")
        
        # 检查发布配置
        if self._检查发布配置():
            print("📢 公众号发布: ✅ 已配置")
        else:
            print("📢 公众号发布: ❌ 未配置")
        
        print(f"⏰ 运行状态: {'🟢 运行中' if self.运行中 else '🔴 已停止'}")
        print()


def 主函数():
    """主函数"""
    print("🤖 全自动套利分析系统")
    print("=" * 60)
    print("🎯 功能：自动采集微信文章 → 分析套利信息 → 发布到公众号")
    print()
    
    # 创建系统
    系统 = 全自动套利系统()
    
    while True:
        print("请选择操作:")
        print("1. 启动全自动监听模式")
        print("2. 手动执行一次分析")
        print("3. 查看系统状态")
        print("4. 测试公众号发布")
        print("5. 退出系统")
        
        选择 = input("\n请输入选择 (1-5): ").strip()
        
        if 选择 == "1":
            系统.启动监听模式()
            
        elif 选择 == "2":
            系统.手动执行一次()
            
        elif 选择 == "3":
            系统.显示系统状态()
            
        elif 选择 == "4":
            print("🧪 测试公众号发布功能...")
            if 系统._检查发布配置():
                成功 = 系统.发布器.测试发布()
                if 成功:
                    print("✅ 测试发布成功！")
                else:
                    print("❌ 测试发布失败")
            else:
                print("❌ 请先配置公众号AppID和AppSecret")
            
        elif 选择 == "5":
            print("👋 感谢使用全自动套利系统！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")
        
        print()


if __name__ == "__main__":
    主函数()
