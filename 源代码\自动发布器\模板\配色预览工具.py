# -*- coding: utf-8 -*-
"""
配色预览工具

功能：
1. 🎨 生成配色方案预览页面
2. 🌈 直观显示颜色效果
3. 📝 展示在文章中的实际应用
4. 💾 保存喜欢的配色方案

作者: AI助手
版本: 1.0
更新时间: 2025-07-29
"""

import os
import webbrowser
from datetime import datetime

def 生成配色预览():
    """生成配色方案预览页面"""
    
    配色方案 = {
        '商务蓝调': {
            '主色': '#2c3e50',
            '辅色': '#3498db', 
            '背景': '#ecf0f1',
            '描述': '深蓝色标题 + 亮蓝色强调 + 浅灰背景',
            '适合': '商务、专业、理财类文章',
            '特点': '稳重、专业、可信赖'
        },
        '活力橙红': {
            '主色': '#e74c3c',
            '辅色': '#f39c12',
            '背景': '#fff3cd',
            '描述': '红色标题 + 橙色强调 + 暖黄背景',
            '适合': '热门话题、紧急通知、促销活动',
            '特点': '醒目、活力、紧迫感'
        },
        '自然绿意': {
            '主色': '#27ae60',
            '辅色': '#2ecc71',
            '背景': '#d5f4e6',
            '描述': '深绿标题 + 亮绿强调 + 淡绿背景',
            '适合': '健康、环保、生活类文章',
            '特点': '清新、自然、健康'
        },
        '优雅紫调': {
            '主色': '#8e44ad',
            '辅色': '#9b59b6',
            '背景': '#f4ecf7',
            '描述': '深紫标题 + 亮紫强调 + 淡紫背景',
            '适合': '艺术、文化、高端品牌',
            '特点': '优雅、神秘、高端'
        },
        '简约黑白': {
            '主色': '#34495e',
            '辅色': '#95a5a6',
            '背景': '#ffffff',
            '描述': '深灰标题 + 浅灰强调 + 纯白背景',
            '适合': '极简风格、新闻资讯、科技类',
            '特点': '简洁、现代、专注内容'
        }
    }
    
    # 生成HTML预览页面
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配色方案预览 - 公众号模板</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 32px;
        }}
        .header p {{
            color: #7f8c8d;
            margin: 0;
            font-size: 16px;
        }}
        .color-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }}
        .color-card {{
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        .color-card:hover {{
            transform: translateY(-5px);
        }}
        .color-header {{
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }}
        .color-name {{
            font-size: 20px;
            font-weight: bold;
            margin: 0 0 8px 0;
        }}
        .color-desc {{
            color: #666;
            margin: 0 0 15px 0;
            font-size: 14px;
        }}
        .color-tags {{
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }}
        .color-tag {{
            background: #f1f2f6;
            color: #2f3542;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }}
        .color-palette {{
            display: flex;
            height: 60px;
        }}
        .color-swatch {{
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }}
        .article-preview {{
            padding: 25px;
        }}
        .preview-title {{
            margin: 0 0 15px 0;
            font-weight: bold;
        }}
        .preview-summary {{
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 14px;
        }}
        .preview-content {{
            margin: 15px 0;
        }}
        .preview-h2 {{
            margin: 20px 0 10px 0;
            font-weight: bold;
        }}
        .preview-emphasis {{
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 3px;
        }}
        .usage-info {{
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-top: 40px;
        }}
        .usage-title {{
            color: #2c3e50;
            font-size: 24px;
            margin: 0 0 20px 0;
            text-align: center;
        }}
        .usage-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        .usage-item {{
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 配色方案预览</h1>
        <p>为你的公众号文章选择最合适的配色方案</p>
        <p style="font-size: 14px; color: #95a5a6;">生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}</p>
    </div>

    <div class="color-grid">"""
    
    # 为每个配色方案生成预览卡片
    for 方案名, 配色 in 配色方案.items():
        html_content += f"""
        <div class="color-card">
            <div class="color-header">
                <h3 class="color-name" style="color: {配色['主色']};">{方案名}</h3>
                <p class="color-desc">{配色['描述']}</p>
                <div class="color-tags">
                    <span class="color-tag">💡 {配色['特点']}</span>
                    <span class="color-tag">📝 {配色['适合']}</span>
                </div>
            </div>
            
            <div class="color-palette">
                <div class="color-swatch" style="background: {配色['主色']};">
                    主色<br>{配色['主色']}
                </div>
                <div class="color-swatch" style="background: {配色['辅色']};">
                    辅色<br>{配色['辅色']}
                </div>
                <div class="color-swatch" style="background: {配色['背景']}; color: #333;">
                    背景<br>{配色['背景']}
                </div>
            </div>
            
            <div class="article-preview">
                <h2 class="preview-title" style="color: {配色['主色']}; font-size: 18px;">
                    📰 示例文章标题
                </h2>
                
                <div class="preview-summary" style="background: {配色['背景']}; border-left: 4px solid {配色['辅色']};">
                    📝 这是文章摘要部分，展示配色在实际应用中的效果。
                </div>
                
                <div class="preview-content">
                    <h3 class="preview-h2" style="color: {配色['主色']}; font-size: 16px;">
                        🔍 二级标题示例
                    </h3>
                    <p style="margin: 10px 0; color: #333; font-size: 14px;">
                        这是正文内容，包含
                        <span class="preview-emphasis" style="color: {配色['辅色']}; background: {配色['背景']};">
                            重点强调文字
                        </span>
                        的展示效果。
                    </p>
                </div>
            </div>
        </div>"""
    
    html_content += """
    </div>
    
    <div class="usage-info">
        <h2 class="usage-title">💡 如何选择配色方案</h2>
        <div class="usage-grid">
            <div class="usage-item" style="background: #ecf0f1;">
                <h4 style="color: #2c3e50; margin: 0 0 10px 0;">🏢 商务专业类</h4>
                <p style="margin: 0; font-size: 14px; color: #555;">
                    推荐：商务蓝调、简约黑白<br>
                    适合：投资理财、企业资讯、专业分析
                </p>
            </div>
            <div class="usage-item" style="background: #fff3cd;">
                <h4 style="color: #e74c3c; margin: 0 0 10px 0;">🔥 热点话题类</h4>
                <p style="margin: 0; font-size: 14px; color: #555;">
                    推荐：活力橙红<br>
                    适合：热门新闻、紧急通知、促销活动
                </p>
            </div>
            <div class="usage-item" style="background: #d5f4e6;">
                <h4 style="color: #27ae60; margin: 0 0 10px 0;">🌱 生活健康类</h4>
                <p style="margin: 0; font-size: 14px; color: #555;">
                    推荐：自然绿意<br>
                    适合：健康养生、环保生活、美食分享
                </p>
            </div>
            <div class="usage-item" style="background: #f4ecf7;">
                <h4 style="color: #8e44ad; margin: 0 0 10px 0;">🎨 文艺创意类</h4>
                <p style="margin: 0; font-size: 14px; color: #555;">
                    推荐：优雅紫调<br>
                    适合：艺术文化、创意设计、高端品牌
                </p>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
            <h4 style="color: #2c3e50; margin: 0 0 15px 0;">🎯 选择建议</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: left;">
                <div>
                    <strong style="color: #3498db;">📊 内容类型</strong><br>
                    <small style="color: #666;">根据文章主题选择对应配色</small>
                </div>
                <div>
                    <strong style="color: #e74c3c;">👥 目标读者</strong><br>
                    <small style="color: #666;">考虑读者群体的喜好偏向</small>
                </div>
                <div>
                    <strong style="color: #27ae60;">🎨 品牌调性</strong><br>
                    <small style="color: #666;">保持与公众号整体风格一致</small>
                </div>
                <div>
                    <strong style="color: #8e44ad;">📱 阅读场景</strong><br>
                    <small style="color: #666;">考虑手机端的显示效果</small>
                </div>
            </div>
        </div>
    </div>
    
    <div style="text-align: center; margin: 40px 0; color: #95a5a6;">
        <p>💡 提示：点击上方配色卡片可以看到详细的颜色代码</p>
        <p>🔧 在自定义模板管理器中选择对应编号即可应用配色方案</p>
    </div>
</body>
</html>"""
    
    # 保存预览文件
    预览文件 = f"配色方案预览_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    with open(预览文件, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"🎨 配色预览页面已生成: {预览文件}")
    
    # 自动打开预览
    try:
        webbrowser.open(f"file:///{os.path.abspath(预览文件)}")
        print("🌐 已在浏览器中打开预览页面")
    except:
        print("⚠️  无法自动打开浏览器，请手动打开预览文件")
    
    return 预览文件

def 主函数():
    """主函数"""
    print("🎨 配色预览工具")
    print("=" * 40)
    print("📋 功能说明:")
    print("  • 生成直观的配色方案预览页面")
    print("  • 展示每种配色在文章中的实际效果")
    print("  • 提供配色选择建议和使用指南")
    print()
    
    确认 = input("是否生成配色预览页面？(y/n): ").lower().strip()
    
    if 确认 in ['y', 'yes', '是', '确认']:
        预览文件 = 生成配色预览()
        print(f"\n✅ 预览页面生成完成！")
        print(f"📁 文件位置: {os.path.abspath(预览文件)}")
        print(f"💡 你可以根据预览效果选择喜欢的配色方案")
    else:
        print("❌ 已取消生成")

if __name__ == "__main__":
    主函数()
