{"投资理财": {"高级极简风": {"标题样式": "<h1 style=\"color: #2c3e50; font-size: 24px; text-align: center; margin: 20px 0; font-weight: 300; letter-spacing: 1px;\">{title}</h1>", "内容模板": "\n<div style=\"font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;\">\n    {title_section}\n    \n    <div style=\"background: #ecf0f1; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db;\">\n        <p style=\"margin: 0; font-size: 16px;\">📝 {summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"text-align: center; margin: 30px 0; padding: 15px; border-top: 1px solid #eee;\">\n        <p style=\"color: #999; margin: 0; font-size: 14px;\">感谢阅读 | 欢迎分享</p>\n    </div>\n</div>\n                ", "段落样式": "<p style=\"margin: 15px 0; font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;\">{content}</p>", "标题样式_h2": "<h2 style=\"color: #2c3e50; font-size: 20px; margin: 25px 0 15px 0; font-weight: 500;\">{content}</h2>", "标题样式_h3": "<h3 style=\"color: #3498db; font-size: 18px; margin: 20px 0 10px 0;\">{content}</h3>", "强调样式": "<strong style=\"color: #3498db; font-weight: bold;\">{content}</strong>"}}, "红绯": {"红绯": {"标题样式": "<h1 style=\"\n                font-size: 30px;\n                font-weight: bold;\n                line-height: 40px;\n                color: #333;\n                text-align: center;\n                margin: 30px 0;\n                padding: 20px 0;\n                border-bottom: 2px solid #e8e8e8;\n                position: relative;\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n    font-size: 16px;\n    color: #333;\n    line-height: 1.8;\n    letter-spacing: 0.05em;\n    max-width: 750px;\n    margin: 0 auto;\n    padding: 20px;\n    background: #fff;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        border-radius: 12px;\n        padding: 20px;\n        margin: 25px 0;\n        color: white;\n        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n    \">\n        <div style=\"\n            display: flex;\n            align-items: center;\n            margin-bottom: 10px;\n        \">\n            <span style=\"\n                font-size: 20px;\n                margin-right: 10px;\n            \">💡</span>\n            <strong style=\"font-size: 18px;\">核心要点</strong>\n        </div>\n        <p style=\"\n            margin: 0;\n            font-size: 16px;\n            line-height: 1.6;\n            opacity: 0.95;\n        \">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 40px;\n        padding: 25px;\n        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n        border-radius: 12px;\n        text-align: center;\n        border: 1px solid #e1e8ed;\n    \">\n        <p style=\"\n            margin: 0;\n            color: #666;\n            font-size: 14px;\n            font-style: italic;\n        \">\n            <span style=\"font-size: 16px;\">✨</span> \n            感谢您的阅读，如果觉得有用请点赞分享\n            <span style=\"font-size: 16px;\">✨</span>\n        </p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 16px 0;\n                text-indent: 2em;\n                line-height: 1.8;\n                color: #333;\n                font-size: 16px;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 24px;\n                font-weight: bold;\n                color: #2c3e50;\n                margin: 35px 0 20px 0;\n                padding: 15px 0 15px 20px;\n                border-left: 5px solid #3498db;\n                background: linear-gradient(90deg, rgba(52, 152, 219, 0.1) 0%, rgba(52, 152, 219, 0.05) 50%, transparent 100%);\n                border-radius: 0 8px 8px 0;\n                position: relative;\n            \">\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 10px;\n                    font-size: 20px;\n                \">📌</span>\n                {content}\n            </h2>", "标题样式_h3": "<h3 style=\"\n                font-size: 20px;\n                font-weight: 600;\n                color: #34495e;\n                margin: 25px 0 15px 0;\n                padding: 10px 0;\n                border-bottom: 2px solid #ecf0f1;\n                position: relative;\n            \">\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 8px;\n                    font-size: 16px;\n                \">🔸</span>\n                {content}\n            </h3>", "列表样式": "<ul style=\"\n                margin: 20px 0;\n                padding-left: 0;\n                list-style: none;\n            \">{items}</ul>", "列表项样式": "<li style=\"\n                margin: 12px 0;\n                padding: 12px 20px;\n                background: #f8f9fa;\n                border-left: 4px solid #3498db;\n                border-radius: 0 8px 8px 0;\n                position: relative;\n                transition: all 0.3s ease;\n            \">\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 10px;\n                    color: #3498db;\n                    font-weight: bold;\n                \">▶</span>\n                {content}\n            </li>", "强调样式": "<strong style=\"\n                color: #e74c3c;\n                font-weight: bold;\n                background: linear-gradient(120deg, rgba(231, 76, 60, 0.1) 0%, rgba(231, 76, 60, 0.05) 100%);\n                padding: 2px 6px;\n                border-radius: 4px;\n                border-bottom: 2px solid rgba(231, 76, 60, 0.3);\n            \">{content}</strong>", "引用样式": "<blockquote style=\"\n                margin: 25px 0;\n                padding: 20px 25px;\n                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n                border-left: 5px solid #6c757d;\n                border-radius: 0 12px 12px 0;\n                font-style: italic;\n                color: #495057;\n                position: relative;\n                box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            \">\n                <div style=\"\n                    position: absolute;\n                    top: 15px;\n                    left: -15px;\n                    width: 30px;\n                    height: 30px;\n                    background: #6c757d;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 16px;\n                \">💬</div>\n                <div style=\"margin-left: 20px;\">\n                    {content}\n                </div>\n            </blockquote>", "代码样式": "<code style=\"\n                background: #f1f2f6;\n                color: #e74c3c;\n                padding: 3px 8px;\n                border-radius: 4px;\n                font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;\n                font-size: 14px;\n                border: 1px solid #e1e8ed;\n            \">{content}</code>", "分割线样式": "<hr style=\"\n                border: none;\n                height: 2px;\n                background: linear-gradient(90deg, transparent 0%, #3498db 50%, transparent 100%);\n                margin: 30px 0;\n                position: relative;\n            \">", "图片样式": "<img style=\"\n                max-width: 100%;\n                height: auto;\n                border-radius: 12px;\n                box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n                margin: 20px 0;\n                display: block;\n                margin-left: auto;\n                margin-right: auto;\n            \" src=\"{src}\" alt=\"{alt}\">"}, "mdnice风格": {"标题样式": "<h1 style=\"\n                font-size: 32px;\n                font-weight: bold;\n                line-height: 1.4;\n                color: #d63031;\n                text-align: center;\n                margin: 30px 0;\n                padding: 20px 0;\n                border-bottom: 3px solid #fab1a0;\n                position: relative;\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n    font-size: 16px;\n    color: #2d3436;\n    line-height: 1.8;\n    max-width: 750px;\n    margin: 0 auto;\n    padding: 20px;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n        border-radius: 15px;\n        padding: 25px;\n        margin: 30px 0;\n        color: white;\n        box-shadow: 0 10px 30px rgba(232, 67, 147, 0.3);\n        position: relative;\n    \">\n        <div style=\"\n            position: absolute;\n            top: -10px;\n            left: 20px;\n            width: 20px;\n            height: 20px;\n            background: #e84393;\n            transform: rotate(45deg);\n        \"></div>\n        <div style=\"\n            display: flex;\n            align-items: center;\n            margin-bottom: 15px;\n        \">\n            <span style=\"\n                font-size: 24px;\n                margin-right: 12px;\n            \">🌸</span>\n            <strong style=\"font-size: 20px;\">核心观点</strong>\n        </div>\n        <p style=\"\n            margin: 0;\n            font-size: 17px;\n            line-height: 1.6;\n            opacity: 0.95;\n        \">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 50px;\n        padding: 30px;\n        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n        border-radius: 20px;\n        text-align: center;\n        border: 2px solid #e17055;\n        position: relative;\n    \">\n        <div style=\"\n            position: absolute;\n            top: -15px;\n            left: 50%;\n            transform: translateX(-50%);\n            background: #e17055;\n            color: white;\n            padding: 8px 20px;\n            border-radius: 20px;\n            font-size: 14px;\n            font-weight: bold;\n        \">感谢阅读</div>\n        <p style=\"\n            margin: 20px 0 0 0;\n            color: #2d3436;\n            font-size: 16px;\n            font-weight: 500;\n        \">\n            <span style=\"font-size: 20px;\">🌹</span> \n            如果觉得有用，请点赞分享\n            <span style=\"font-size: 20px;\">🌹</span>\n        </p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 18px 0;\n                text-indent: 2em;\n                line-height: 1.8;\n                color: #2d3436;\n                font-size: 16px;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 26px;\n                font-weight: bold;\n                color: #d63031;\n                margin: 40px 0 25px 0;\n                padding: 20px 0 20px 25px;\n                border-left: 6px solid #fd79a8;\n                background: linear-gradient(90deg, rgba(253, 121, 168, 0.15) 0%, rgba(253, 121, 168, 0.05) 50%, transparent 100%);\n                border-radius: 0 15px 15px 0;\n                position: relative;\n            \">\n                <span style=\"\n                    position: absolute;\n                    left: -3px;\n                    top: 50%;\n                    transform: translateY(-50%);\n                    width: 12px;\n                    height: 12px;\n                    background: #fd79a8;\n                    border-radius: 50%;\n                \"></span>\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 12px;\n                    font-size: 22px;\n                \">🌺</span>\n                {content}\n            </h2>", "标题样式_h3": "<h3 style=\"\n                font-size: 22px;\n                font-weight: 600;\n                color: #e84393;\n                margin: 30px 0 18px 0;\n                padding: 15px 0;\n                border-bottom: 3px solid #fab1a0;\n                position: relative;\n            \">\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 10px;\n                    font-size: 18px;\n                \">🌸</span>\n                {content}\n                <span style=\"\n                    position: absolute;\n                    bottom: -3px;\n                    left: 0;\n                    width: 60px;\n                    height: 3px;\n                    background: #e84393;\n                \"></span>\n            </h3>", "强调样式": "<strong style=\"\n                color: #d63031;\n                font-weight: bold;\n                background: linear-gradient(120deg, rgba(214, 48, 49, 0.1) 0%, rgba(214, 48, 49, 0.05) 100%);\n                padding: 3px 8px;\n                border-radius: 6px;\n                border-bottom: 2px solid rgba(214, 48, 49, 0.3);\n                box-shadow: 0 2px 4px rgba(214, 48, 49, 0.1);\n            \">{content}</strong>", "列表项样式": "<li style=\"\n                margin: 15px 0;\n                padding: 15px 25px;\n                background: linear-gradient(135deg, #ffeaa7 0%, rgba(255, 234, 167, 0.3) 100%);\n                border-left: 5px solid #fd79a8;\n                border-radius: 0 12px 12px 0;\n                position: relative;\n                transition: all 0.3s ease;\n                box-shadow: 0 3px 10px rgba(253, 121, 168, 0.2);\n            \">\n                <span style=\"\n                    position: absolute;\n                    left: -8px;\n                    top: 50%;\n                    transform: translateY(-50%);\n                    width: 16px;\n                    height: 16px;\n                    background: #fd79a8;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 10px;\n                    font-weight: bold;\n                \">•</span>\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 12px;\n                    color: #e84393;\n                    font-weight: bold;\n                    font-size: 16px;\n                \">▶</span>\n                {content}\n            </li>", "引用样式": "<blockquote style=\"\n                margin: 30px 0;\n                padding: 25px 30px;\n                background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n                border-left: 6px solid #e84393;\n                border-radius: 0 20px 20px 0;\n                font-style: italic;\n                color: #2d3436;\n                position: relative;\n                box-shadow: 0 5px 20px rgba(232, 67, 147, 0.2);\n            \">\n                <div style=\"\n                    position: absolute;\n                    top: 20px;\n                    left: -20px;\n                    width: 40px;\n                    height: 40px;\n                    background: #e84393;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 20px;\n                    box-shadow: 0 3px 10px rgba(232, 67, 147, 0.3);\n                \">💭</div>\n                <div style=\"margin-left: 30px; font-size: 17px; line-height: 1.7;\">\n                    {content}\n                </div>\n            </blockquote>"}}, "橙心": {"mdnice风格": {"标题样式": "<h1 style=\"\n                font-size: 32px;\n                font-weight: bold;\n                line-height: 1.4;\n                color: #d63031;\n                text-align: center;\n                margin: 30px 0;\n                padding: 20px 0;\n                border-bottom: 3px solid #fab1a0;\n                position: relative;\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n    font-size: 16px;\n    color: #2d3436;\n    line-height: 1.8;\n    max-width: 750px;\n    margin: 0 auto;\n    padding: 20px;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n        border-radius: 15px;\n        padding: 25px;\n        margin: 30px 0;\n        color: white;\n        box-shadow: 0 10px 30px rgba(232, 67, 147, 0.3);\n        position: relative;\n    \">\n        <div style=\"\n            position: absolute;\n            top: -10px;\n            left: 20px;\n            width: 20px;\n            height: 20px;\n            background: #e84393;\n            transform: rotate(45deg);\n        \"></div>\n        <div style=\"\n            display: flex;\n            align-items: center;\n            margin-bottom: 15px;\n        \">\n            <span style=\"\n                font-size: 24px;\n                margin-right: 12px;\n            \">🌸</span>\n            <strong style=\"font-size: 20px;\">核心观点</strong>\n        </div>\n        <p style=\"\n            margin: 0;\n            font-size: 17px;\n            line-height: 1.6;\n            opacity: 0.95;\n        \">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 50px;\n        padding: 30px;\n        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n        border-radius: 20px;\n        text-align: center;\n        border: 2px solid #e17055;\n        position: relative;\n    \">\n        <div style=\"\n            position: absolute;\n            top: -15px;\n            left: 50%;\n            transform: translateX(-50%);\n            background: #e17055;\n            color: white;\n            padding: 8px 20px;\n            border-radius: 20px;\n            font-size: 14px;\n            font-weight: bold;\n        \">感谢阅读</div>\n        <p style=\"\n            margin: 20px 0 0 0;\n            color: #2d3436;\n            font-size: 16px;\n            font-weight: 500;\n        \">\n            <span style=\"font-size: 20px;\">🌹</span> \n            如果觉得有用，请点赞分享\n            <span style=\"font-size: 20px;\">🌹</span>\n        </p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 18px 0;\n                text-indent: 2em;\n                line-height: 1.8;\n                color: #2d3436;\n                font-size: 16px;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 26px;\n                font-weight: bold;\n                color: #d63031;\n                margin: 40px 0 25px 0;\n                padding: 20px 0 20px 25px;\n                border-left: 6px solid #fd79a8;\n                background: linear-gradient(90deg, rgba(253, 121, 168, 0.15) 0%, rgba(253, 121, 168, 0.05) 50%, transparent 100%);\n                border-radius: 0 15px 15px 0;\n                position: relative;\n            \">\n                <span style=\"\n                    position: absolute;\n                    left: -3px;\n                    top: 50%;\n                    transform: translateY(-50%);\n                    width: 12px;\n                    height: 12px;\n                    background: #fd79a8;\n                    border-radius: 50%;\n                \"></span>\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 12px;\n                    font-size: 22px;\n                \">🌺</span>\n                {content}\n            </h2>", "标题样式_h3": "<h3 style=\"\n                font-size: 22px;\n                font-weight: 600;\n                color: #e84393;\n                margin: 30px 0 18px 0;\n                padding: 15px 0;\n                border-bottom: 3px solid #fab1a0;\n                position: relative;\n            \">\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 10px;\n                    font-size: 18px;\n                \">🌸</span>\n                {content}\n                <span style=\"\n                    position: absolute;\n                    bottom: -3px;\n                    left: 0;\n                    width: 60px;\n                    height: 3px;\n                    background: #e84393;\n                \"></span>\n            </h3>", "强调样式": "<strong style=\"\n                color: #d63031;\n                font-weight: bold;\n                background: linear-gradient(120deg, rgba(214, 48, 49, 0.1) 0%, rgba(214, 48, 49, 0.05) 100%);\n                padding: 3px 8px;\n                border-radius: 6px;\n                border-bottom: 2px solid rgba(214, 48, 49, 0.3);\n                box-shadow: 0 2px 4px rgba(214, 48, 49, 0.1);\n            \">{content}</strong>", "列表项样式": "<li style=\"\n                margin: 15px 0;\n                padding: 15px 25px;\n                background: linear-gradient(135deg, #ffeaa7 0%, rgba(255, 234, 167, 0.3) 100%);\n                border-left: 5px solid #fd79a8;\n                border-radius: 0 12px 12px 0;\n                position: relative;\n                transition: all 0.3s ease;\n                box-shadow: 0 3px 10px rgba(253, 121, 168, 0.2);\n            \">\n                <span style=\"\n                    position: absolute;\n                    left: -8px;\n                    top: 50%;\n                    transform: translateY(-50%);\n                    width: 16px;\n                    height: 16px;\n                    background: #fd79a8;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 10px;\n                    font-weight: bold;\n                \">•</span>\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 12px;\n                    color: #e84393;\n                    font-weight: bold;\n                    font-size: 16px;\n                \">▶</span>\n                {content}\n            </li>", "引用样式": "<blockquote style=\"\n                margin: 30px 0;\n                padding: 25px 30px;\n                background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n                border-left: 6px solid #e84393;\n                border-radius: 0 20px 20px 0;\n                font-style: italic;\n                color: #2d3436;\n                position: relative;\n                box-shadow: 0 5px 20px rgba(232, 67, 147, 0.2);\n            \">\n                <div style=\"\n                    position: absolute;\n                    top: 20px;\n                    left: -20px;\n                    width: 40px;\n                    height: 40px;\n                    background: #e84393;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 20px;\n                    box-shadow: 0 3px 10px rgba(232, 67, 147, 0.3);\n                \">💭</div>\n                <div style=\"margin-left: 30px; font-size: 17px; line-height: 1.7;\">\n                    {content}\n                </div>\n            </blockquote>"}}, "绿意": {"mdnice风格": {"标题样式": "<h1 style=\"\n                font-size: 30px;\n                font-weight: bold;\n                color: #00b894;\n                text-align: center;\n                margin: 30px 0;\n                padding: 20px 0;\n                border-bottom: 3px solid #55efc4;\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, sans-serif;\n    font-size: 16px;\n    color: #2d3436;\n    line-height: 1.8;\n    max-width: 750px;\n    margin: 0 auto;\n    padding: 20px;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);\n        border-radius: 15px;\n        padding: 25px;\n        margin: 30px 0;\n        color: white;\n        box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);\n    \">\n        <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n            <span style=\"font-size: 22px; margin-right: 12px;\">🌱</span>\n            <strong style=\"font-size: 18px;\">核心观点</strong>\n        </div>\n        <p style=\"margin: 0; font-size: 16px; line-height: 1.6;\">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 40px;\n        padding: 25px;\n        background: linear-gradient(135deg, #d1f2eb 0%, #a3e4d7 100%);\n        border-radius: 12px;\n        text-align: center;\n        border: 1px solid #55efc4;\n    \">\n        <p style=\"margin: 0; color: #00b894; font-size: 15px;\">\n            <span style=\"font-size: 18px;\">🍃</span> \n            绿色生活，健康分享\n            <span style=\"font-size: 18px;\">🍃</span>\n        </p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 16px 0;\n                text-indent: 2em;\n                line-height: 1.8;\n                color: #2d3436;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 24px;\n                font-weight: bold;\n                color: #00b894;\n                margin: 35px 0 20px 0;\n                padding: 18px 0 18px 25px;\n                border-left: 5px solid #55efc4;\n                background: linear-gradient(90deg, rgba(85, 239, 196, 0.1) 0%, transparent 100%);\n            \">\n                <span style=\"margin-right: 10px;\">🌿</span>\n                {content}\n            </h2>", "强调样式": "<strong style=\"\n                color: #00b894;\n                font-weight: bold;\n                background: rgba(85, 239, 196, 0.2);\n                padding: 2px 6px;\n                border-radius: 4px;\n            \">{content}</strong>"}}, "蓝湖": {"mdnice风格": {"标题样式": "<h1 style=\"\n                font-size: 30px;\n                font-weight: bold;\n                line-height: 1.4;\n                color: #0984e3;\n                text-align: center;\n                margin: 30px 0;\n                padding: 25px 0;\n                border-bottom: 3px solid #74b9ff;\n                background: linear-gradient(135deg, rgba(116, 185, 255, 0.1) 0%, transparent 100%);\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n    font-size: 16px;\n    color: #2d3436;\n    line-height: 1.8;\n    max-width: 750px;\n    margin: 0 auto;\n    padding: 20px;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n        border-radius: 12px;\n        padding: 25px;\n        margin: 30px 0;\n        color: white;\n        box-shadow: 0 8px 25px rgba(9, 132, 227, 0.3);\n    \">\n        <div style=\"\n            display: flex;\n            align-items: center;\n            margin-bottom: 15px;\n        \">\n            <span style=\"font-size: 22px; margin-right: 12px;\">💡</span>\n            <strong style=\"font-size: 18px;\">核心要点</strong>\n        </div>\n        <p style=\"margin: 0; font-size: 16px; line-height: 1.6; opacity: 0.95;\">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 40px;\n        padding: 25px;\n        background: linear-gradient(135deg, #ddd6fe 0%, #a5b4fc 100%);\n        border-radius: 12px;\n        text-align: center;\n        border: 1px solid #74b9ff;\n    \">\n        <p style=\"margin: 0; color: #0984e3; font-size: 15px; font-weight: 500;\">\n            <span style=\"font-size: 18px;\">⭐</span> \n            专业内容，值得收藏分享\n            <span style=\"font-size: 18px;\">⭐</span>\n        </p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 16px 0;\n                text-indent: 2em;\n                line-height: 1.8;\n                color: #2d3436;\n                font-size: 16px;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 24px;\n                font-weight: bold;\n                color: #0984e3;\n                margin: 35px 0 20px 0;\n                padding: 18px 0 18px 25px;\n                border-left: 5px solid #74b9ff;\n                background: linear-gradient(90deg, rgba(116, 185, 255, 0.1) 0%, transparent 100%);\n                border-radius: 0 10px 10px 0;\n            \">\n                <span style=\"margin-right: 10px; font-size: 20px;\">🔹</span>\n                {content}\n            </h2>", "强调样式": "<strong style=\"\n                color: #0984e3;\n                font-weight: bold;\n                background: rgba(116, 185, 255, 0.15);\n                padding: 2px 6px;\n                border-radius: 4px;\n                border-bottom: 2px solid rgba(9, 132, 227, 0.3);\n            \">{content}</strong>"}}, "紫霞": {"mdnice风格": {"标题样式": "<h1 style=\"\n                font-size: 32px;\n                font-weight: bold;\n                line-height: 1.4;\n                color: #d63031;\n                text-align: center;\n                margin: 30px 0;\n                padding: 20px 0;\n                border-bottom: 3px solid #fab1a0;\n                position: relative;\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n    font-size: 16px;\n    color: #2d3436;\n    line-height: 1.8;\n    max-width: 750px;\n    margin: 0 auto;\n    padding: 20px;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n        border-radius: 15px;\n        padding: 25px;\n        margin: 30px 0;\n        color: white;\n        box-shadow: 0 10px 30px rgba(232, 67, 147, 0.3);\n        position: relative;\n    \">\n        <div style=\"\n            position: absolute;\n            top: -10px;\n            left: 20px;\n            width: 20px;\n            height: 20px;\n            background: #e84393;\n            transform: rotate(45deg);\n        \"></div>\n        <div style=\"\n            display: flex;\n            align-items: center;\n            margin-bottom: 15px;\n        \">\n            <span style=\"\n                font-size: 24px;\n                margin-right: 12px;\n            \">🌸</span>\n            <strong style=\"font-size: 20px;\">核心观点</strong>\n        </div>\n        <p style=\"\n            margin: 0;\n            font-size: 17px;\n            line-height: 1.6;\n            opacity: 0.95;\n        \">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 50px;\n        padding: 30px;\n        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n        border-radius: 20px;\n        text-align: center;\n        border: 2px solid #e17055;\n        position: relative;\n    \">\n        <div style=\"\n            position: absolute;\n            top: -15px;\n            left: 50%;\n            transform: translateX(-50%);\n            background: #e17055;\n            color: white;\n            padding: 8px 20px;\n            border-radius: 20px;\n            font-size: 14px;\n            font-weight: bold;\n        \">感谢阅读</div>\n        <p style=\"\n            margin: 20px 0 0 0;\n            color: #2d3436;\n            font-size: 16px;\n            font-weight: 500;\n        \">\n            <span style=\"font-size: 20px;\">🌹</span> \n            如果觉得有用，请点赞分享\n            <span style=\"font-size: 20px;\">🌹</span>\n        </p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 18px 0;\n                text-indent: 2em;\n                line-height: 1.8;\n                color: #2d3436;\n                font-size: 16px;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 26px;\n                font-weight: bold;\n                color: #d63031;\n                margin: 40px 0 25px 0;\n                padding: 20px 0 20px 25px;\n                border-left: 6px solid #fd79a8;\n                background: linear-gradient(90deg, rgba(253, 121, 168, 0.15) 0%, rgba(253, 121, 168, 0.05) 50%, transparent 100%);\n                border-radius: 0 15px 15px 0;\n                position: relative;\n            \">\n                <span style=\"\n                    position: absolute;\n                    left: -3px;\n                    top: 50%;\n                    transform: translateY(-50%);\n                    width: 12px;\n                    height: 12px;\n                    background: #fd79a8;\n                    border-radius: 50%;\n                \"></span>\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 12px;\n                    font-size: 22px;\n                \">🌺</span>\n                {content}\n            </h2>", "标题样式_h3": "<h3 style=\"\n                font-size: 22px;\n                font-weight: 600;\n                color: #e84393;\n                margin: 30px 0 18px 0;\n                padding: 15px 0;\n                border-bottom: 3px solid #fab1a0;\n                position: relative;\n            \">\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 10px;\n                    font-size: 18px;\n                \">🌸</span>\n                {content}\n                <span style=\"\n                    position: absolute;\n                    bottom: -3px;\n                    left: 0;\n                    width: 60px;\n                    height: 3px;\n                    background: #e84393;\n                \"></span>\n            </h3>", "强调样式": "<strong style=\"\n                color: #d63031;\n                font-weight: bold;\n                background: linear-gradient(120deg, rgba(214, 48, 49, 0.1) 0%, rgba(214, 48, 49, 0.05) 100%);\n                padding: 3px 8px;\n                border-radius: 6px;\n                border-bottom: 2px solid rgba(214, 48, 49, 0.3);\n                box-shadow: 0 2px 4px rgba(214, 48, 49, 0.1);\n            \">{content}</strong>", "列表项样式": "<li style=\"\n                margin: 15px 0;\n                padding: 15px 25px;\n                background: linear-gradient(135deg, #ffeaa7 0%, rgba(255, 234, 167, 0.3) 100%);\n                border-left: 5px solid #fd79a8;\n                border-radius: 0 12px 12px 0;\n                position: relative;\n                transition: all 0.3s ease;\n                box-shadow: 0 3px 10px rgba(253, 121, 168, 0.2);\n            \">\n                <span style=\"\n                    position: absolute;\n                    left: -8px;\n                    top: 50%;\n                    transform: translateY(-50%);\n                    width: 16px;\n                    height: 16px;\n                    background: #fd79a8;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 10px;\n                    font-weight: bold;\n                \">•</span>\n                <span style=\"\n                    display: inline-block;\n                    margin-right: 12px;\n                    color: #e84393;\n                    font-weight: bold;\n                    font-size: 16px;\n                \">▶</span>\n                {content}\n            </li>", "引用样式": "<blockquote style=\"\n                margin: 30px 0;\n                padding: 25px 30px;\n                background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n                border-left: 6px solid #e84393;\n                border-radius: 0 20px 20px 0;\n                font-style: italic;\n                color: #2d3436;\n                position: relative;\n                box-shadow: 0 5px 20px rgba(232, 67, 147, 0.2);\n            \">\n                <div style=\"\n                    position: absolute;\n                    top: 20px;\n                    left: -20px;\n                    width: 40px;\n                    height: 40px;\n                    background: #e84393;\n                    border-radius: 50%;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: white;\n                    font-size: 20px;\n                    box-shadow: 0 3px 10px rgba(232, 67, 147, 0.3);\n                \">💭</div>\n                <div style=\"margin-left: 30px; font-size: 17px; line-height: 1.7;\">\n                    {content}\n                </div>\n            </blockquote>"}}, "简约": {"mdnice风格": {"标题样式": "<h1 style=\"\n                font-size: 28px;\n                font-weight: 300;\n                color: #2d3436;\n                text-align: center;\n                margin: 30px 0;\n                padding: 20px 0;\n                border-bottom: 1px solid #ddd;\n            \">{title}</h1>", "内容模板": "\n<div style=\"\n    font-family: -apple-system, sans-serif;\n    font-size: 16px;\n    color: #2d3436;\n    line-height: 1.7;\n    max-width: 700px;\n    margin: 0 auto;\n    padding: 20px;\n\">\n    {title_section}\n    \n    <div style=\"\n        background: #f8f9fa;\n        border-left: 4px solid #6c757d;\n        padding: 20px;\n        margin: 25px 0;\n        border-radius: 0 8px 8px 0;\n    \">\n        <p style=\"margin: 0; font-size: 15px; color: #495057;\">{summary}</p>\n    </div>\n    \n    {content_sections}\n    \n    <div style=\"\n        margin-top: 40px;\n        padding: 20px;\n        text-align: center;\n        border-top: 1px solid #dee2e6;\n        color: #6c757d;\n    \">\n        <p style=\"margin: 0; font-size: 14px;\">— 感谢阅读 —</p>\n    </div>\n</div>\n            ", "段落样式": "<p style=\"\n                margin: 15px 0;\n                line-height: 1.7;\n                color: #2d3436;\n            \">{content}</p>", "标题样式_h2": "<h2 style=\"\n                font-size: 22px;\n                font-weight: 500;\n                color: #2d3436;\n                margin: 30px 0 15px 0;\n                padding-bottom: 10px;\n                border-bottom: 1px solid #dee2e6;\n            \">{content}</h2>", "强调样式": "<strong style=\"\n                color: #495057;\n                font-weight: 600;\n                background: #f8f9fa;\n                padding: 1px 4px;\n                border-radius: 2px;\n            \">{content}</strong>"}}}