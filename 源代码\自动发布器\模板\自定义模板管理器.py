# -*- coding: utf-8 -*-
"""
自定义模板管理器

功能：
1. 🎨 添加自定义模板样式
2. 📝 编辑现有模板
3. 🔍 预览模板效果
4. 💾 保存模板配置
5. 📋 模板导入导出

作者: AI助手
版本: 1.0
更新时间: 2025-07-29
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Optional

# 添加源代码路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))

from 模板管理器 import 模板管理器

class 自定义模板管理器:
    """自定义模板管理器"""
    
    def __init__(self):
        """初始化自定义模板管理器"""
        self.模板管理器 = 模板管理器()
        self.自定义模板文件 = "配置文件/自定义模板.json"
        self.模板预览目录 = "模板预览"
        
        # 创建必要目录
        os.makedirs("配置文件", exist_ok=True)
        os.makedirs(self.模板预览目录, exist_ok=True)
        
        # 加载自定义模板
        self._加载自定义模板()
        
        print("🎨 自定义模板管理器初始化完成")
    
    def _加载自定义模板(self):
        """加载自定义模板配置"""
        if os.path.exists(self.自定义模板文件):
            try:
                with open(self.自定义模板文件, 'r', encoding='utf-8') as f:
                    自定义模板 = json.load(f)
                
                # 将自定义模板添加到模板管理器
                for 主题, 样式字典 in 自定义模板.items():
                    for 样式名, 模板配置 in 样式字典.items():
                        self.模板管理器.添加自定义模板(主题, 样式名, 模板配置)
                
                print(f"✅ 已加载自定义模板配置")
            except Exception as e:
                print(f"⚠️  加载自定义模板失败: {str(e)}")
    
    def _保存自定义模板(self, 主题: str, 样式: str, 模板配置: Dict):
        """保存自定义模板到文件"""
        自定义模板 = {}
        
        # 读取现有配置
        if os.path.exists(self.自定义模板文件):
            try:
                with open(self.自定义模板文件, 'r', encoding='utf-8') as f:
                    自定义模板 = json.load(f)
            except:
                自定义模板 = {}
        
        # 添加新模板
        if 主题 not in 自定义模板:
            自定义模板[主题] = {}
        
        自定义模板[主题][样式] = 模板配置
        
        # 保存到文件
        with open(self.自定义模板文件, 'w', encoding='utf-8') as f:
            json.dump(自定义模板, f, ensure_ascii=False, indent=2)
        
        print(f"💾 已保存自定义模板: {主题} - {样式}")
    
    def 创建自定义模板(self):
        """交互式创建自定义模板"""
        print("\n🎨 创建自定义模板")
        print("=" * 50)
        
        # 1. 选择主题
        print("📋 可用主题:")
        现有主题 = self.模板管理器.获取可用主题()
        for i, 主题 in enumerate(现有主题, 1):
            print(f"  {i}. {主题}")
        print(f"  {len(现有主题) + 1}. 创建新主题")
        
        while True:
            try:
                选择 = input(f"\n请选择主题 (1-{len(现有主题) + 1}): ").strip()
                选择编号 = int(选择)
                
                if 1 <= 选择编号 <= len(现有主题):
                    主题 = 现有主题[选择编号 - 1]
                    break
                elif 选择编号 == len(现有主题) + 1:
                    主题 = input("请输入新主题名称: ").strip()
                    if 主题:
                        break
                    else:
                        print("❌ 主题名称不能为空")
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")
        
        # 2. 输入样式名称
        样式名 = input(f"\n请输入样式名称 (如: 我的风格): ").strip()
        if not 样式名:
            样式名 = "自定义样式"
        
        # 3. 选择创建方式
        print(f"\n🛠️  创建方式:")
        print("  1. 从现有模板修改")
        print("  2. 从零开始创建")
        print("  3. 使用模板向导")
        
        while True:
            try:
                方式 = input("请选择创建方式 (1-3): ").strip()
                if 方式 in ['1', '2', '3']:
                    break
                else:
                    print("❌ 请选择 1、2 或 3")
            except:
                print("❌ 请输入有效选择")
        
        if 方式 == '1':
            模板配置 = self._从现有模板修改(主题)
        elif 方式 == '2':
            模板配置 = self._从零创建模板()
        else:
            模板配置 = self._使用模板向导()
        
        if 模板配置:
            # 添加到模板管理器
            self.模板管理器.添加自定义模板(主题, 样式名, 模板配置)
            
            # 保存到文件
            self._保存自定义模板(主题, 样式名, 模板配置)
            
            # 预览效果
            self._预览模板效果(主题, 样式名, 模板配置)
            
            print(f"✅ 自定义模板创建成功: {主题} - {样式名}")
            return True
        else:
            print("❌ 模板创建失败")
            return False
    
    def _从现有模板修改(self, 目标主题: str) -> Optional[Dict]:
        """从现有模板修改"""
        print(f"\n📝 从现有模板修改")
        
        # 显示可用模板
        print("📋 可用模板:")
        模板选项 = []
        编号 = 1
        
        for 主题 in self.模板管理器.获取可用主题():
            样式列表 = self.模板管理器.获取主题样式(主题)
            for 样式 in 样式列表:
                print(f"  {编号}. {主题} - {样式}")
                模板选项.append((主题, 样式))
                编号 += 1
        
        while True:
            try:
                选择 = input(f"请选择基础模板 (1-{len(模板选项)}): ").strip()
                选择编号 = int(选择)
                
                if 1 <= 选择编号 <= len(模板选项):
                    基础主题, 基础样式 = 模板选项[选择编号 - 1]
                    break
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")
        
        # 获取基础模板配置
        基础模板 = self.模板管理器.模板库.get(基础主题, {}).get(基础样式, {})
        
        if not 基础模板:
            print("❌ 无法获取基础模板")
            return None
        
        # 复制基础模板
        新模板 = 基础模板.copy()
        
        print(f"\n✅ 已复制基础模板: {基础主题} - {基础样式}")
        print("📝 现在可以修改模板配置...")
        
        # 交互式修改
        return self._交互式修改模板(新模板)
    
    def _从零创建模板(self) -> Optional[Dict]:
        """从零开始创建模板"""
        print(f"\n🆕 从零开始创建模板")
        
        模板配置 = {
            '标题样式': '',
            '内容模板': '',
            '段落样式': '',
            '标题样式_h2': '',
            '标题样式_h3': '',
            '列表样式': '',
            '列表项样式': '',
            '强调样式': '',
            '引用样式': ''
        }
        
        print("📝 请输入各部分的HTML样式代码...")
        print("💡 提示: 可以使用 {title}, {content}, {summary} 等占位符")
        
        for 样式名 in 模板配置.keys():
            print(f"\n🎨 {样式名}:")
            if 样式名 == '内容模板':
                print("💡 这是主模板，需要包含 {title_section}, {summary}, {content_sections}")
            
            样式代码 = input(f"请输入{样式名}的HTML代码 (回车跳过): ").strip()
            if 样式代码:
                模板配置[样式名] = 样式代码
        
        # 检查必要字段
        if not 模板配置['标题样式'] or not 模板配置['内容模板']:
            print("❌ 标题样式和内容模板是必需的")
            return None
        
        return 模板配置
    
    def _使用模板向导(self) -> Optional[Dict]:
        """使用模板向导创建"""
        print(f"\n🧙‍♂️ 模板向导")
        
        # 1. 选择风格
        print("🎨 选择整体风格:")
        风格选项 = {
            '1': '简约现代',
            '2': '商务专业', 
            '3': '温馨亲和',
            '4': '科技未来',
            '5': '文艺清新'
        }
        
        for 编号, 风格 in 风格选项.items():
            print(f"  {编号}. {风格}")
        
        风格选择 = input("请选择风格 (1-5): ").strip()
        选择风格 = 风格选项.get(风格选择, '简约现代')
        
        # 2. 选择配色方案
        print(f"\n🌈 选择配色方案:")
        配色选项 = {
            '1': {
                '名称': '商务蓝调',
                '描述': '深蓝色标题 + 亮蓝色强调 + 浅灰背景',
                '适合': '商务、专业、理财类文章',
                '主色': '#2c3e50', '辅色': '#3498db', '背景': '#ecf0f1'
            },
            '2': {
                '名称': '活力橙红',
                '描述': '红色标题 + 橙色强调 + 暖黄背景',
                '适合': '热门话题、紧急通知、促销活动',
                '主色': '#e74c3c', '辅色': '#f39c12', '背景': '#fff3cd'
            },
            '3': {
                '名称': '自然绿意',
                '描述': '深绿标题 + 亮绿强调 + 淡绿背景',
                '适合': '健康、环保、生活类文章',
                '主色': '#27ae60', '辅色': '#2ecc71', '背景': '#d5f4e6'
            },
            '4': {
                '名称': '优雅紫调',
                '描述': '深紫标题 + 亮紫强调 + 淡紫背景',
                '适合': '艺术、文化、高端品牌',
                '主色': '#8e44ad', '辅色': '#9b59b6', '背景': '#f4ecf7'
            },
            '5': {
                '名称': '简约黑白',
                '描述': '深灰标题 + 浅灰强调 + 纯白背景',
                '适合': '极简风格、新闻资讯、科技类',
                '主色': '#34495e', '辅色': '#95a5a6', '背景': '#ffffff'
            }
        }

        for 编号, 配色 in 配色选项.items():
            print(f"  {编号}. 🎨 {配色['名称']}")
            print(f"      📝 {配色['描述']}")
            print(f"      💡 适合: {配色['适合']}")
            print()
        
        配色选择 = input("请选择配色 (1-5): ").strip()
        选择配色 = 配色选项.get(配色选择, 配色选项['1'])
        
        # 3. 生成模板
        return self._生成向导模板(选择风格, 选择配色)
    
    def _生成向导模板(self, 风格: str, 配色: Dict) -> Dict:
        """根据向导选择生成模板"""
        
        基础样式 = f"font-family: 'Microsoft YaHei', sans-serif; color: {配色['主色']}; line-height: 1.7;"
        
        if 风格 == '简约现代':
            模板配置 = {
                '标题样式': f'<h1 style="color: {配色["主色"]}; font-size: 24px; text-align: center; margin: 20px 0; font-weight: 300; letter-spacing: 1px;">{{title}}</h1>',
                '内容模板': f'''
<div style="{基础样式}">
    {{title_section}}
    
    <div style="background: {配色["背景"]}; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid {配色["辅色"]};">
        <p style="margin: 0; font-size: 16px;">📝 {{summary}}</p>
    </div>
    
    {{content_sections}}
    
    <div style="text-align: center; margin: 30px 0; padding: 15px; border-top: 1px solid #eee;">
        <p style="color: #999; margin: 0; font-size: 14px;">感谢阅读 | 欢迎分享</p>
    </div>
</div>
                ''',
                '段落样式': f'<p style="margin: 15px 0; {基础样式}">{{content}}</p>',
                '标题样式_h2': f'<h2 style="color: {配色["主色"]}; font-size: 20px; margin: 25px 0 15px 0; font-weight: 500;">{{content}}</h2>',
                '标题样式_h3': f'<h3 style="color: {配色["辅色"]}; font-size: 18px; margin: 20px 0 10px 0;">{{content}}</h3>',
                '强调样式': f'<strong style="color: {配色["辅色"]}; font-weight: bold;">{{content}}</strong>'
            }
        
        elif 风格 == '商务专业':
            模板配置 = {
                '标题样式': f'<h1 style="color: {配色["主色"]}; font-size: 26px; text-align: center; margin: 25px 0; font-weight: bold; border-bottom: 3px solid {配色["辅色"]}; padding-bottom: 15px;">{{title}}</h1>',
                '内容模板': f'''
<div style="{基础样式} max-width: 800px; margin: 0 auto;">
    {{title_section}}
    
    <div style="background: linear-gradient(135deg, {配色["辅色"]} 0%, {配色["主色"]} 100%); padding: 20px; border-radius: 10px; margin: 25px 0;">
        <p style="color: white; margin: 0; font-size: 17px; text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">
            💼 <strong>核心观点</strong>：{{summary}}
        </p>
    </div>
    
    {{content_sections}}
    
    <div style="background: {配色["背景"]}; padding: 20px; border-radius: 8px; margin: 30px 0; text-align: center;">
        <p style="color: {配色["主色"]}; margin: 0; font-weight: 500;">🤝 专业 | 可靠 | 值得信赖</p>
    </div>
</div>
                ''',
                '段落样式': f'<p style="margin: 18px 0; text-indent: 2em; {基础样式}">{{content}}</p>',
                '标题样式_h2': f'<h2 style="color: {配色["主色"]}; font-size: 22px; margin: 30px 0 18px 0; font-weight: bold; border-left: 5px solid {配色["辅色"]}; padding-left: 20px;">{{content}}</h2>',
                '强调样式': f'<strong style="color: {配色["辅色"]}; font-weight: bold; background: {配色["背景"]}; padding: 2px 6px; border-radius: 3px;">{{content}}</strong>'
            }
        
        # 可以继续添加其他风格...
        else:
            # 默认简约风格
            模板配置 = {
                '标题样式': f'<h1 style="color: {配色["主色"]}; font-size: 24px; text-align: center; margin: 20px 0;">{{title}}</h1>',
                '内容模板': f'''
<div style="{基础样式}">
    {{title_section}}
    <div style="background: {配色["背景"]}; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p style="margin: 0;">{{summary}}</p>
    </div>
    {{content_sections}}
</div>
                ''',
                '段落样式': f'<p style="margin: 15px 0; {基础样式}">{{content}}</p>',
                '标题样式_h2': f'<h2 style="color: {配色["主色"]}; font-size: 20px; margin: 20px 0 10px 0;">{{content}}</h2>'
            }
        
        return 模板配置
    
    def _交互式修改模板(self, 模板配置: Dict) -> Dict:
        """交互式修改模板配置"""
        print(f"\n🔧 交互式修改模板")
        print("📝 当前模板配置项:")
        
        配置项列表 = list(模板配置.keys())
        for i, 项目 in enumerate(配置项列表, 1):
            当前值 = 模板配置[项目]
            预览值 = 当前值[:50] + "..." if len(当前值) > 50 else 当前值
            print(f"  {i}. {项目}: {预览值}")
        
        while True:
            选择 = input(f"\n请选择要修改的项目 (1-{len(配置项列表)}, 回车完成): ").strip()
            
            if not 选择:
                break
            
            try:
                选择编号 = int(选择)
                if 1 <= 选择编号 <= len(配置项列表):
                    项目名 = 配置项列表[选择编号 - 1]
                    当前值 = 模板配置[项目名]
                    
                    print(f"\n🎨 修改 {项目名}")
                    print(f"当前值: {当前值}")
                    
                    新值 = input("请输入新值 (回车保持不变): ").strip()
                    if 新值:
                        模板配置[项目名] = 新值
                        print(f"✅ 已更新 {项目名}")
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")
        
        return 模板配置
    
    def _预览模板效果(self, 主题: str, 样式: str, 模板配置: Dict):
        """预览模板效果"""
        print(f"\n🔍 预览模板效果: {主题} - {样式}")
        
        # 示例文章
        示例标题 = "这是一个示例标题"
        示例内容 = '''
## 这是二级标题
这是一段示例内容，用来展示模板的效果。

### 这是三级标题
- 这是列表项1
- 这是列表项2

**这是强调文本**，这是普通文本。
        '''
        
        try:
            # 临时添加模板
            self.模板管理器.添加自定义模板(主题, 样式, 模板配置)
            
            # 应用模板
            格式化内容 = self.模板管理器.应用模板(
                标题=示例标题,
                内容=示例内容,
                主题=主题,
                样式=样式
            )
            
            # 保存预览文件
            预览文件 = os.path.join(
                self.模板预览目录, 
                f"预览_{主题}_{样式}_{datetime.now().strftime('%H%M%S')}.html"
            )
            
            with open(预览文件, 'w', encoding='utf-8') as f:
                f.write(f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板预览 - {主题} {样式}</title>
    <style>
        body {{ margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #eee; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>模板预览</h1>
            <p>主题: {主题} | 样式: {样式} | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        {格式化内容}
    </div>
</body>
</html>""")
            
            print(f"✅ 预览文件已保存: {预览文件}")
            
            # 询问是否打开预览
            打开预览 = input("是否打开预览文件？(y/n): ").lower().strip()
            if 打开预览 in ['y', 'yes', '是']:
                import webbrowser
                webbrowser.open(f"file:///{os.path.abspath(预览文件)}")
            
        except Exception as e:
            print(f"❌ 预览失败: {str(e)}")

def 主函数():
    """主函数"""
    print("🎨 自定义模板管理器")
    print("=" * 50)
    
    管理器 = 自定义模板管理器()
    
    while True:
        print(f"\n📋 功能菜单:")
        print("  1. 创建自定义模板")
        print("  2. 查看现有模板")
        print("  3. 预览模板效果")
        print("  4. 导出模板配置")
        print("  0. 退出")
        
        选择 = input("\n请选择功能 (0-4): ").strip()
        
        if 选择 == '1':
            管理器.创建自定义模板()
        elif 选择 == '2':
            print(f"\n📋 现有模板:")
            for 主题 in 管理器.模板管理器.获取可用主题():
                样式列表 = 管理器.模板管理器.获取主题样式(主题)
                print(f"  🎨 {主题}: {', '.join(样式列表)}")
        elif 选择 == '3':
            print("🔍 预览功能开发中...")
        elif 选择 == '4':
            print("📤 导出功能开发中...")
        elif 选择 == '0':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    主函数()
