#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信公众号自动发布系统 - 主程序入口
集成AI配图和自动发布功能的统一入口

功能特点：
1. 🌸 Pollinations AI优先配图 - 根据关键词生成相关图片
2. 🎯 智能主题匹配 - 科技、商务、自然等主题自动识别
3. 📱 微信公众号自动发布 - 支持草稿和正式发布
4. 🔄 多级降级机制 - 确保配图成功率100%
5. 📊 完整日志记录 - 详细的操作日志和错误追踪

作者: AI配图自动发布系统
版本: 2.0
更新时间: 2025-07-28
"""

import os
import sys
from datetime import datetime

# 添加源代码路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))

def 显示系统信息():
    """显示系统信息和功能介绍"""
    print("🚀 微信公众号自动发布系统 v2.0")
    print("=" * 60)
    print("📅 启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)
    
    print("🎯 核心功能:")
    print("   1. 🌸 Pollinations AI配图 - 根据关键词生成相关图片")
    print("   2. 🤗 HuggingFace AI配图 - 备用AI生成服务")
    print("   3. 🎯 主题化Picsum配图 - 主题相关真实图片")
    print("   4. 🎲 随机Picsum配图 - 高质量随机图片")
    print("   5. 💻 本地生成配图 - 最终保底方案")
    print("   6. 📱 微信公众号发布 - 支持草稿和正式发布")
    
    print("\n🔧 配图优先级:")
    print("   Pollinations AI → HuggingFace AI → 主题化Picsum → 随机Picsum → 本地生成")
    
    print("\n📁 项目结构:")
    print("   ├── 源代码/自动发布器/     # 核心功能模块")
    print("   ├── 配置文件夹/           # 系统配置文件")
    print("   └── 微信公众号自动发布系统.py  # 主程序入口")

def 验证系统环境():
    """验证系统环境和依赖"""
    print("\n🔍 验证系统环境...")
    
    try:
        # 检查核心模块
        from 微信自动发布器 import 微信自动发布器
        from AI配图系统 import AI配图生成器
        print("✅ 核心模块导入成功")
        
        # 检查配置文件
        配置路径 = os.path.join(os.path.dirname(__file__), '配置文件夹')
        if os.path.exists(配置路径):
            print("✅ 配置文件夹存在")
        else:
            print("⚠️  配置文件夹不存在，将使用默认配置")
        
        # 验证发布器配置
        发布器 = 微信自动发布器()
        配置有效, 错误列表 = 发布器.验证系统配置()
        
        if 配置有效:
            print("✅ 微信公众号配置验证通过")
            return True
        else:
            print("❌ 微信公众号配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            return False
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        print("请确保所有依赖已正确安装")
        return False
    except Exception as e:
        print(f"❌ 系统验证失败: {str(e)}")
        return False

def 创建示例文章():
    """读取当天的套利分析报告"""
    import os
    import glob
    from datetime import date

    # 获取当前脚本的根目录
    当前目录 = os.path.dirname(os.path.abspath(__file__))
    根目录 = os.path.dirname(os.path.dirname(当前目录))
    采集结果目录 = os.path.join(根目录, "采集结果")

    # 查找当天的套利分析报告
    今日 = date.today().strftime('%Y%m%d')
    报告模式 = os.path.join(采集结果目录, f"套利分析报告_{今日}_*.txt")
    报告文件列表 = glob.glob(报告模式)

    if not 报告文件列表:
        print(f"❌ 未找到今日({今日})的套利分析报告")
        print(f"📁 查找路径: {报告模式}")

        # 查找最近的报告作为备选
        所有报告模式 = os.path.join(采集结果目录, "套利分析报告_*.txt")
        所有报告 = glob.glob(所有报告模式)

        if 所有报告:
            最新报告 = max(所有报告, key=os.path.getmtime)
            print(f"💡 找到最新报告: {os.path.basename(最新报告)}")

            确认 = input("是否使用最新报告进行发布？(y/N): ").strip().lower()
            if 确认 not in ['y', 'yes', '是']:
                raise Exception("未找到当天的套利分析报告，且用户拒绝使用最新报告")

            报告文件 = 最新报告
        else:
            raise Exception("未找到任何套利分析报告文件")
    else:
        # 使用最新的当天报告
        报告文件 = max(报告文件列表, key=os.path.getmtime)
        print(f"✅ 找到当天报告: {os.path.basename(报告文件)}")

    # 读取报告内容
    try:
        with open(报告文件, 'r', encoding='utf-8') as f:
            原始内容 = f.read()

        # 转换为发布格式
        标题, 发布内容 = 转换套利报告格式(原始内容)

        return {
            '标题': 标题,
            '内容': 发布内容,
            '元数据': {
                '作者': '套利分析助手',
                '来源': '智能分析系统',
                '标签': ['投资分析', '套利机会', '新股打新', 'REIT投资', '可转债'],
                '分类': '投资理财'
            }
        }

    except Exception as e:
        print(f"❌ 读取报告文件失败: {e}")
        raise Exception(f"无法读取套利分析报告: {e}")

def 转换套利报告格式(原始内容):
    """将套利报告转换为适合发布的格式"""
    from datetime import datetime

    # 生成标题
    今日 = datetime.now().strftime('%m月%d日')
    标题 = f"📊 {今日}每日套利分析报告"

    # 转换内容格式
    发布内容 = f"""# 🎯 今日套利分析速览

📅 **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M')}
🔍 **分析来源**: 优质投资公众号文章智能分析
📊 **涵盖领域**: 新股打新 | REIT投资 | 可转债套利

---

"""

    # 处理原始内容，保留重要信息
    lines = 原始内容.split('\n')
    当前章节 = ""

    for line in lines:
        line = line.strip()

        if not line or line.startswith('=') or line.startswith('-'):
            continue

        # 处理各种标题和内容
        if line.startswith('📰 文章'):
            当前章节 = "文章"
            发布内容 += f"\n## {line}\n"
        elif line.startswith('🆕 新股信息'):
            当前章节 = "新股"
            发布内容 += f"\n### 🆕 **新股打新机会**\n"
        elif line.startswith('🏢 REIT信息'):
            当前章节 = "REIT"
            发布内容 += f"\n### 🏢 **REIT投资机会**\n"
        elif line.startswith('💰 转债信息'):
            当前章节 = "转债"
            发布内容 += f"\n### 💰 **可转债套利**\n"
        elif line.startswith('💡 套利机会'):
            当前章节 = "套利"
            发布内容 += f"\n### 💡 **套利机会分析**\n"
        elif line.startswith('📋 操作建议'):
            当前章节 = "建议"
            发布内容 += f"\n### 📋 **操作建议**\n"
        elif line.startswith('⏰ 重要时间'):
            当前章节 = "时间"
            发布内容 += f"\n### ⏰ **重要时间节点**\n"
        elif line.startswith('   •'):
            # 处理列表项 - 这是主要内容
            内容项 = line[4:].strip()

            # 分离标题和内容
            if '：' in 内容项:
                标题部分, 内容部分 = 内容项.split('：', 1)
                发布内容 += f"\n**{标题部分.strip()}**\n"
                if 内容部分.strip():
                    # 处理长内容
                    if len(内容部分) > 200:
                        内容部分 = 内容部分[:200] + "..."
                    发布内容 += f"{内容部分.strip()}\n"
            else:
                # 没有冒号分隔的情况
                if len(内容项) > 150:
                    内容项 = 内容项[:150] + "..."
                发布内容 += f"• {内容项}\n"
        elif line.startswith('🕒 发布时间'):
            发布内容 += f"\n**发布时间**: {line.split(':', 1)[1].strip()}\n"
        elif line.startswith('📅 生成时间'):
            # 跳过，我们用自己的时间
            continue
        elif line.startswith('📄 分析文章数'):
            发布内容 += f"\n📊 **数据来源**: {line.split(':', 1)[1].strip()}篇优质投资文章\n"
        elif line.startswith('📊 套利信息分析报告'):
            # 跳过原标题
            continue
        else:
            # 处理其他内容行
            if line and not line.startswith('📰') and not line.startswith('🆕') and not line.startswith('🏢') and not line.startswith('💰') and not line.startswith('💡') and not line.startswith('📋') and not line.startswith('⏰'):
                if len(line) > 100:
                    line = line[:100] + "..."
                发布内容 += f"{line}\n"

    # 添加结尾
    发布内容 += f"""

---

## ⚠️ 风险提示

本分析仅供参考，投资有风险，入市需谨慎。请根据自身风险承受能力谨慎投资。

## 🤖 智能分析说明

本报告基于AI智能分析优质投资文章，自动提取套利机会信息，为您提供投资参考。

**关注我们获取每日最新套利分析！**

#投资分析 #套利机会 #新股打新 #REIT投资 #可转债
"""

    return 标题, 发布内容

def 交互式发布():
    """交互式发布文章"""
    print("\n🚀 启动交互式发布模式")
    print("=" * 50)
    
    try:
        from 微信自动发布器 import 微信自动发布器
        
        发布器 = 微信自动发布器()
        
        while True:
            print("\n📝 请选择操作:")
            print("1. 发布示例文章（推荐）")
            print("2. 发布自定义文章")
            print("3. 测试AI配图功能")
            print("4. 查看系统状态")
            print("0. 退出系统")
            
            选择 = input("\n请输入选项 (0-4): ").strip()
            
            if 选择 == '0':
                print("👋 感谢使用AI配图自动发布系统！")
                break
            elif 选择 == '1':
                发布示例文章(发布器)
            elif 选择 == '2':
                发布自定义文章(发布器)
            elif 选择 == '3':
                测试AI配图功能()
            elif 选择 == '4':
                查看系统状态(发布器)
            else:
                print("❌ 无效选项，请重新选择")
                
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，系统退出")
    except Exception as e:
        print(f"❌ 系统运行异常: {str(e)}")

def 发布示例文章(发布器):
    """发布示例文章"""
    print("\n📝 准备发布示例文章...")
    
    文章数据 = 创建示例文章()
    
    # 发布选项
    发布选项 = {
        '仅草稿': True,  # 默认只创建草稿
        '排版样式': 'tech',
        '启用AI配图': True,
        'AI配图服务': 'smart_free',  # 使用Pollinations优先的智能配图
        '跳过确认': False,  # 需要用户确认
        '显示封面': True
    }
    
    print(f"📄 文章标题: {文章数据['标题']}")
    print(f"🎨 配图服务: {发布选项['AI配图服务']} (Pollinations优先)")
    print(f"📋 发布模式: {'仅草稿' if 发布选项['仅草稿'] else '直接发布'}")
    
    确认 = input("\n是否继续发布？(y/N): ").strip().lower()
    if 确认 not in ['y', 'yes', '是']:
        print("❌ 用户取消发布")
        return
    
    print("\n⏳ 开始发布...")
    开始时间 = datetime.now()
    
    结果 = 发布器.发布文章(文章数据, 发布选项)
    
    结束时间 = datetime.now()
    总耗时 = (结束时间 - 开始时间).total_seconds()
    
    if 结果['success']:
        print(f"\n🎉 发布成功!")
        print(f"📄 草稿ID: {结果['media_id']}")
        print(f"⏱️  总耗时: {总耗时:.2f}秒")
        print(f"\n💡 请登录微信公众号后台查看草稿箱验证效果")
    else:
        print(f"❌ 发布失败: {结果['error_message']}")

def 发布自定义文章(发布器):
    """发布自定义文章"""
    print("\n📝 自定义文章发布")
    print("请输入文章信息（输入空行结束）:")
    
    标题 = input("文章标题: ").strip()
    if not 标题:
        print("❌ 标题不能为空")
        return
    
    print("文章内容（输入'END'结束）:")
    内容行 = []
    while True:
        行 = input()
        if 行.strip() == 'END':
            break
        内容行.append(行)
    
    内容 = '\n'.join(内容行)
    if not 内容.strip():
        print("❌ 内容不能为空")
        return
    
    文章数据 = {
        '标题': 标题,
        '内容': 内容,
        '元数据': {
            '作者': '用户自定义',
            '来源': '手动输入',
            '标签': ['自定义'],
            '分类': '用户内容'
        }
    }
    
    # 发布选项
    发布选项 = {
        '仅草稿': True,
        '排版样式': 'tech',
        '启用AI配图': True,
        'AI配图服务': 'smart_free',
        '跳过确认': True,
        '显示封面': True
    }
    
    print(f"\n⏳ 开始发布文章: {标题}")
    结果 = 发布器.发布文章(文章数据, 发布选项)
    
    if 结果['success']:
        print(f"🎉 发布成功! 草稿ID: {结果['media_id']}")
    else:
        print(f"❌ 发布失败: {结果['error_message']}")

def 测试AI配图功能():
    """测试AI配图功能"""
    print("\n🎨 测试AI配图功能")
    print("=" * 40)
    
    try:
        from AI配图系统 import AI配图生成器
        
        配图生成器 = AI配图生成器()
        
        关键词 = input("请输入配图关键词: ").strip()
        if not 关键词:
            print("❌ 关键词不能为空")
            return
        
        print(f"\n🔍 正在为关键词 '{关键词}' 生成配图...")
        
        图片路径 = 配图生成器._智能免费配图(关键词, width=800, height=600)
        
        if 图片路径 and os.path.exists(图片路径):
            文件名 = os.path.basename(图片路径)
            文件大小 = os.path.getsize(图片路径) / 1024
            
            print(f"✅ 配图生成成功!")
            print(f"📁 文件名: {文件名}")
            print(f"📊 文件大小: {文件大小:.1f} KB")
            print(f"📂 保存路径: {图片路径}")
            
            # 判断使用的服务
            if 'pollinations_' in 文件名:
                print(f"🌸 使用服务: Pollinations AI (最佳效果)")
            elif 'huggingface_' in 文件名:
                print(f"🤗 使用服务: HuggingFace AI (很好效果)")
            elif 'themed_picsum_' in 文件名:
                print(f"🎯 使用服务: 主题化Picsum (良好效果)")
            elif 'picsum_' in 文件名:
                print(f"🎲 使用服务: 随机Picsum (保底效果)")
            else:
                print(f"💻 使用服务: 本地生成 (最终保底)")
        else:
            print("❌ 配图生成失败")
            
    except Exception as e:
        print(f"❌ 配图测试失败: {str(e)}")

def 查看系统状态(发布器):
    """查看系统状态"""
    print("\n📊 系统状态检查")
    print("=" * 40)
    
    try:
        # 检查配置
        配置有效, 错误列表 = 发布器.验证系统配置()
        
        if 配置有效:
            print("✅ 微信公众号配置: 正常")
        else:
            print("❌ 微信公众号配置: 异常")
            for error in 错误列表:
                print(f"   - {error}")
        
        # 检查目录
        自动发布器目录 = os.path.join(os.path.dirname(__file__), '源代码', '自动发布器')
        if os.path.exists(自动发布器目录):
            print("✅ 自动发布器目录: 存在")
        else:
            print("❌ 自动发布器目录: 不存在")
        
        # 检查图片目录
        图片目录1 = os.path.join(自动发布器目录, 'downloaded_images')
        图片目录2 = os.path.join(自动发布器目录, 'generated_images')
        
        if os.path.exists(图片目录1):
            图片数量1 = len([f for f in os.listdir(图片目录1) if f.endswith(('.jpg', '.png'))])
            print(f"✅ 下载图片目录: {图片数量1} 张图片")
        else:
            print("⚠️  下载图片目录: 不存在")
        
        if os.path.exists(图片目录2):
            图片数量2 = len([f for f in os.listdir(图片目录2) if f.endswith(('.jpg', '.png'))])
            print(f"✅ 生成图片目录: {图片数量2} 张图片")
        else:
            print("⚠️  生成图片目录: 不存在")
            
    except Exception as e:
        print(f"❌ 状态检查失败: {str(e)}")

def 直接发布投资文章():
    """直接发布投资套利文章到草稿"""
    print("🚀 开始发布投资套利文章到草稿...")

    try:
        from 微信自动发布器 import 微信自动发布器

        发布器 = 微信自动发布器()

        # 创建投资套利文章
        文章数据 = 创建示例文章()

        # 发布选项
        发布选项 = {
            '仅草稿': True,  # 只创建草稿
            '排版样式': 'business',  # 使用商务风格排版
            '启用AI配图': True,
            'AI配图服务': 'smart_free',  # 使用智能免费配图
            '跳过确认': True,  # 跳过用户确认
            '显示封面': True
        }

        print(f"📄 文章标题: {文章数据['标题']}")
        print(f"📝 文章字数: {len(文章数据['内容'])} 字")
        print(f"🎨 配图服务: {发布选项['AI配图服务']}")
        print(f"📋 发布模式: 仅草稿")
        print(f"🎯 排版样式: {发布选项['排版样式']}")

        print("\n⏳ 开始发布...")
        开始时间 = datetime.now()

        结果 = 发布器.发布文章(文章数据, 发布选项)

        结束时间 = datetime.now()
        总耗时 = (结束时间 - 开始时间).total_seconds()

        if 结果['success']:
            print(f"\n🎉 发布成功!")
            print(f"📄 草稿ID: {结果['media_id']}")
            print(f"⏱️  总耗时: {总耗时:.2f}秒")
            print(f"\n💡 请登录微信公众号后台查看草稿箱验证效果")
            print(f"🔍 检查要点:")
            print(f"   - 文字排版是否自动适配")
            print(f"   - AI配图是否成功生成")
            print(f"   - 整体版面是否美观")
        else:
            print(f"❌ 发布失败: {结果['error_message']}")

    except Exception as e:
        print(f"❌ 发布过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        print("🚀 微信公众号自动发布系统 - 投资文章测试")
        print("=" * 60)
        print("📅 启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print("=" * 60)

        if not 验证系统环境():
            print("\n❌ 系统环境验证失败，请检查配置后重试")
            return

        print("\n✅ 系统环境验证通过，开始发布...")

        # 直接发布投资文章
        直接发布投资文章()

    except Exception as e:
        print(f"\n❌ 系统启动失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
