# 套利信息提取规则配置文件
# 用于配置套利分析报告的生成规则和限制条件

# 基本配置
基本设置:
  报告语言: "中文"
  输出格式: "markdown"
  时间戳格式: "%Y%m%d_%H%M%S"
  保存路径: "采集结果"

# 文章生成限制规则
文章生成限制:
  # 字数限制
  字数控制:
    最小字数: 800
    最大字数: 2500
    警告阈值: 2000  # 超过此字数给出警告
    
  # 段落结构限制
  段落结构:
    最大段落数: 8
    每段最大字数: 400
    每段最小字数: 50
    
  # 内容深度控制
  内容深度:
    分析层次: "中等"  # 简单/中等/深入
    技术术语使用: "适中"  # 少量/适中/大量
    数据引用要求: "必须"  # 可选/建议/必须

# 套利信息提取规则
套利信息提取:
  # 核心提取内容
  核心内容:
    - 套利机会识别
    - 具体操作策略  
    - 关键数据信息
    - 风险因素分析
    - 时间敏感信息
    
  # 提取重点关注
  重点关注:
    标的信息:
      - 标的名称
      - 标的代码
      - 当前价格
      - 目标价格
      - 预期收益率
      
    操作策略:
      - 买入条件
      - 卖出条件
      - 止损位置
      - 仓位建议
      - 操作时机
      
    风险控制:
      - 主要风险点
      - 风险等级评估
      - 应对措施
      - 最大损失预期
      
  # 数据验证要求
  数据验证:
    价格数据: "必须包含具体数字"
    时间信息: "必须包含明确日期"
    收益预期: "必须有量化指标"

# 报告结构模板
报告结构:
  # 标准报告格式
  标准格式:
    - 标题: "套利机会分析报告"
    - 摘要: "核心套利机会总结"
    - 详细分析: "分类别详细分析"
    - 操作建议: "具体操作指导"
    - 风险提示: "风险因素说明"
    - 时效性说明: "信息有效期"
    
  # 各部分内容要求
  内容要求:
    摘要部分:
      字数限制: 200
      必须包含: ["套利类型", "预期收益", "风险等级"]
      
    详细分析:
      字数限制: 1200
      必须包含: ["市场分析", "标的分析", "策略分析"]
      
    操作建议:
      字数限制: 400
      必须包含: ["具体步骤", "时机选择", "仓位管理"]
      
    风险提示:
      字数限制: 300
      必须包含: ["主要风险", "应对方案", "止损建议"]

# 内容质量控制
质量控制:
  # 信息准确性要求
  准确性要求:
    数据来源: "必须标注"
    时效性: "必须说明"
    可靠性: "必须评估"
    
  # 内容完整性检查
  完整性检查:
    必需信息:
      - 套利标的
      - 操作策略
      - 风险评估
      - 预期收益
      - 操作时间
      
  # 表达规范
  表达规范:
    语言风格: "专业客观"
    术语使用: "准确规范"
    数据表达: "具体量化"
    建议表述: "明确可操作"

# 风险提示规则
风险提示:
  # 必须包含的风险提示
  标准风险提示:
    - "投资有风险，入市需谨慎"
    - "套利策略存在失效风险"
    - "市场变化可能影响预期收益"
    - "建议根据个人风险承受能力操作"
    
  # 特殊风险提示
  特殊风险:
    高风险策略:
      风险等级: "高"
      特别提示: "此策略风险较高，建议谨慎操作"
      
    时效性强:
      时效提示: "此信息时效性强，请及时关注市场变化"
      
    复杂操作:
      操作提示: "此策略操作复杂，建议充分了解后再操作"

# 禁止内容规则
禁止内容:
  # 绝对禁止的表述
  禁止表述:
    - "保证盈利"
    - "无风险套利"
    - "100%成功"
    - "稳赚不赔"
    - "必然获利"
    
  # 限制使用的词汇
  限制词汇:
    - 原词: "肯定"
      替换为: "预期"
    - 原词: "一定"
      替换为: "可能"
    - 原词: "绝对"
      替换为: "相对"
    - 原词: "永远"
      替换为: "当前"
    
  # 避免的内容类型
  避免内容:
    - 个股推荐（除非作为案例分析）
    - 具体买卖时点建议
    - 过于乐观的收益预测
    - 忽视风险的表述

# 合规要求
合规要求:
  # 免责声明
  免责声明:
    位置: "报告末尾"
    内容: |
      本报告仅供参考，不构成投资建议。
      投资者应根据自身情况谨慎决策。
      市场有风险，投资需谨慎。
      
  # 信息来源说明
  信息来源:
    要求: "必须标注信息来源"
    格式: "信息来源：[具体来源]"
    时效: "必须标注信息获取时间"
    
  # 作者信息
  作者信息:
    署名: "AI分析系统"
    生成时间: "必须标注"
    版本信息: "可选"

# 输出格式配置
输出格式:
  # 文件命名规则
  文件命名:
    前缀: "套利分析报告"
    时间戳: true
    扩展名: ".md"
    
  # Markdown格式要求
  markdown格式:
    标题层级: "最多4级"
    列表格式: "使用-符号"
    表格要求: "必要时使用表格展示数据"
    代码块: "用于展示具体数据"
    
  # 特殊格式标记
  特殊标记:
    重要信息: "**粗体**"
    风险提示: "⚠️ 风险提示"
    操作建议: "💡 操作建议"
    数据信息: "📊 数据信息"

# 自定义规则扩展
自定义规则:
  # 用户可自定义的规则
  用户自定义:
    特殊关键词: []  # 用户可添加特别关注的关键词
    排除关键词: []  # 用户可添加需要排除的关键词
    重点标的: []    # 用户可添加重点关注的标的
    
  # 个性化设置
  个性化设置:
    分析深度偏好: "中等"  # 简单/中等/深入
    风险偏好: "保守"      # 激进/平衡/保守
    信息详细程度: "适中"   # 简洁/适中/详细

# AI提示词模板配置
AI提示词模板:
  # 基础提取提示词
  基础提取提示:
    模板: |
      请专门提取以下文章中的套利相关信息，严格按照配置规则执行。

      ## 文章信息
      标题：{标题}
      作者：{作者}

      ## 文章内容：
      {内容}

      ## 提取要求：
      请按以下格式提取套利信息，字数控制在{最小字数}-{最大字数}字之间：

      ### 1. 套利机会识别
      - 提到的具体套利机会（如A股港股价差、可转债套利、分红套利等）
      - 涉及的具体标的名称和代码
      - 套利的基本逻辑

      ### 2. 具体操作策略
      - 买入/卖出的具体条件
      - 价格区间或估值水平
      - 操作时机

      ### 3. 关键数据信息
      - 文章中提到的具体数据（价格、溢价率、涨幅等）
      - 估值水平
      - 市场数据

      ### 4. 风险因素分析
      - 提到的风险点
      - 止损条件
      - 注意事项

      ### 5. 时间敏感信息
      - 即将发生的事件（如新股上市、转债上市等）
      - 时间节点
      - 预期表现

      ## 注意事项：
      - 只提取文章中明确提到的信息，不要添加额外的分析
      - 避免使用禁止词汇：{禁止表述}
      - 必须包含风险提示
      - 如果某个类别没有相关信息，请标注"无相关信息"

  # 总结生成提示词
  总结生成提示:
    模板: |
      基于以下提取的套利信息，请生成一个简洁的结构化总结，字数控制在{摘要字数限制}字以内：

      提取的套利信息：
      {套利信息}

      请按以下JSON格式输出总结：

      ```json
      {{
          "套利机会总数": "数字",
          "主要套利类型": ["类型1", "类型2", "类型3"],
          "重点关注标的": [
              {{
                  "标的名称": "名称",
                  "标的代码": "代码",
                  "套利类型": "类型",
                  "关键数据": "数据",
                  "操作建议": "建议",
                  "风险等级": "低/中/高"
              }}
          ],
          "时间敏感事件": [
              {{
                  "事件": "事件描述",
                  "时间": "时间",
                  "预期": "预期表现"
              }}
          ],
          "关键风险点": ["风险1", "风险2", "风险3"],
          "核心策略": "主要策略总结",
          "风险等级评估": "整体风险等级",
          "建议操作时间": "操作时间建议"
      }}
      ```

      请确保输出有效的JSON格式，并包含必要的风险提示。

# 数据验证规则
数据验证规则:
  # 必需字段验证
  必需字段:
    - 套利标的
    - 操作策略
    - 风险评估
    - 数据来源
    - 生成时间

  # 数据格式验证
  格式验证:
    价格数据: "必须为数字格式"
    日期数据: "必须为YYYY-MM-DD格式"
    百分比数据: "必须包含%符号"
    代码格式: "股票代码必须符合规范"

  # 内容长度验证
  长度验证:
    标题长度: [10, 50]
    摘要长度: [100, 300]
    正文长度: [800, 2500]
    风险提示长度: [50, 200]

# 错误处理规则
错误处理:
  # 数据缺失处理
  数据缺失:
    标的信息缺失: "标注为'信息不完整'"
    价格数据缺失: "标注为'价格待确认'"
    时间信息缺失: "标注为'时间待确认'"

  # 格式错误处理
  格式错误:
    JSON格式错误: "重新生成JSON"
    Markdown格式错误: "修正格式"
    数据格式错误: "标注错误并提示"

  # 内容质量问题
  质量问题:
    内容过短: "要求补充内容"
    内容过长: "要求精简内容"
    风险提示不足: "强制添加风险提示"

# 更新日志配置
更新日志:
  版本: "1.0.0"
  创建日期: "2025-07-29"
  最后更新: "2025-07-29"
  更新说明:
    - "初始版本创建"
    - "包含完整的套利信息提取规则"
    - "支持用户自定义配置"
    - "包含合规要求和风险控制"
