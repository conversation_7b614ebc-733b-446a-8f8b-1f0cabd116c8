# -*- coding: utf-8 -*-
"""
半自动微信采集器

作者: AI助手
日期: 2025-07-29
功能: 结合手动操作和自动处理的微信公众号文章采集器
"""

import os
import sys
import time
import pyperclip
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any
import re
from urllib.parse import urlparse, parse_qs

# 添加配置文件路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(os.path.dirname(当前目录))
配置文件路径 = os.path.join(根目录, "配置文件")
sys.path.append(配置文件路径)


class 半自动微信采集器:
    """半自动微信公众号文章采集器"""
    
    def __init__(self, 采集天数: int = 2):
        """
        初始化采集器
        
        参数:
            采集天数: 采集最近几天的文章，默认2天
        """
        self.采集天数 = 采集天数
        self.截止时间 = datetime.now() - timedelta(days=采集天数)
        self.文章列表 = []
        
        # 创建保存目录
        self.保存目录 = os.path.join(根目录, "采集结果", "微信文章")
        os.makedirs(self.保存目录, exist_ok=True)
        
        print(f"🤖 半自动微信采集器初始化完成")
        print(f"📅 将采集最近 {采集天数} 天的文章")
        print(f"📁 保存目录: {self.保存目录}")
    
    def 监听剪贴板(self, 超时秒数: int = 300):
        """
        监听剪贴板变化，自动处理复制的微信文章链接
        
        参数:
            超时秒数: 监听超时时间，默认5分钟
        """
        print(f"👂 开始监听剪贴板变化...")
        print(f"⏰ 监听时长: {超时秒数} 秒")
        print(f"💡 请在微信中复制文章链接，程序会自动处理")
        print(f"🛑 按 Ctrl+C 停止监听")
        
        上次内容 = ""
        开始时间 = time.time()
        
        try:
            while True:
                # 检查超时
                if time.time() - 开始时间 > 超时秒数:
                    print(f"⏰ 监听超时，已停止")
                    break
                
                # 获取剪贴板内容
                try:
                    当前内容 = pyperclip.paste()
                except:
                    time.sleep(1)
                    continue
                
                # 检查内容是否变化
                if 当前内容 != 上次内容 and 当前内容.strip():
                    上次内容 = 当前内容
                    
                    # 检查是否是微信文章链接
                    if self._是微信文章链接(当前内容):
                        print(f"\n📎 检测到微信文章链接:")
                        print(f"🔗 {当前内容[:100]}...")
                        
                        # 处理文章链接
                        self._处理文章链接(当前内容)
                    
                    elif "mp.weixin.qq.com" in 当前内容:
                        print(f"\n📎 检测到可能的微信链接:")
                        print(f"🔗 {当前内容[:100]}...")
                        
                        # 尝试处理
                        self._处理文章链接(当前内容)
                
                time.sleep(0.5)  # 减少CPU占用
                
        except KeyboardInterrupt:
            print(f"\n⏹️ 用户停止监听")
        
        print(f"\n📊 监听结束，共处理 {len(self.文章列表)} 篇文章")
        return self.文章列表
    
    def _是微信文章链接(self, 内容: str) -> bool:
        """检查是否是微信文章链接"""
        微信域名 = ['mp.weixin.qq.com', 'weixin.qq.com']
        return any(域名 in 内容 for 域名 in 微信域名)
    
    def _处理文章链接(self, 链接: str):
        """处理单个文章链接"""
        try:
            # 清理链接
            链接 = 链接.strip()
            if not 链接.startswith('http'):
                return
            
            print(f"🔄 正在处理文章...")
            
            # 获取文章信息
            文章信息 = self._获取文章信息(链接)
            
            if 文章信息:
                # 检查时间是否符合条件
                if self._检查文章时间(文章信息.get('发布时间', '')):
                    self.文章列表.append(文章信息)
                    print(f"✅ 文章已添加: {文章信息['标题'][:30]}...")
                    
                    # 保存文章
                    self._保存文章(文章信息)
                else:
                    print(f"⏰ 文章时间超出范围: {文章信息.get('发布时间', '未知')}")
            else:
                print(f"❌ 无法获取文章信息")
                
        except Exception as 错误:
            print(f"❌ 处理文章链接失败: {错误}")
    
    def _获取文章信息(self, 链接: str) -> Dict[str, Any]:
        """获取文章基本信息"""
        try:
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            # 请求文章页面
            response = requests.get(链接, headers=headers, timeout=10)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                html = response.text
                
                # 提取文章信息
                文章信息 = {
                    '链接': 链接,
                    '标题': self._提取标题(html),
                    '作者': self._提取作者(html),
                    '发布时间': self._提取发布时间(html),
                    '内容': self._提取内容(html),
                    '来源': '微信公众号',
                    '采集时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                return 文章信息
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as 错误:
            print(f"❌ 获取文章信息失败: {错误}")
            return None
    
    def _提取标题(self, html: str) -> str:
        """从HTML中提取文章标题"""
        import re
        
        # 尝试多种标题提取方式
        patterns = [
            r'<title[^>]*>(.*?)</title>',
            r'<h1[^>]*>(.*?)</h1>',
            r'<h2[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)</h2>',
            r'var msg_title = "(.*?)";',
            r'"title":"([^"]*)"'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE | re.DOTALL)
            if match:
                标题 = match.group(1).strip()
                # 清理HTML标签
                标题 = re.sub(r'<[^>]+>', '', 标题)
                if 标题 and len(标题) > 2:
                    return 标题
        
        return "未知标题"
    
    def _提取作者(self, html: str) -> str:
        """从HTML中提取作者信息"""
        import re
        
        patterns = [
            r'var nickname = "(.*?)";',
            r'"nickname":"([^"]*)"',
            r'<span[^>]*class="[^"]*author[^"]*"[^>]*>(.*?)</span>',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                作者 = match.group(1).strip()
                if 作者:
                    return 作者
        
        return "未知作者"
    
    def _提取发布时间(self, html: str) -> str:
        """从HTML中提取发布时间"""
        import re
        
        patterns = [
            r'var publish_time = "(\d+)";',
            r'"publish_time":(\d+)',
            r'<span[^>]*class="[^"]*time[^"]*"[^>]*>(.*?)</span>',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                时间字符串 = match.group(1).strip()
                
                # 如果是时间戳
                if 时间字符串.isdigit():
                    try:
                        时间戳 = int(时间字符串)
                        发布时间 = datetime.fromtimestamp(时间戳)
                        return 发布时间.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        continue
                
                # 如果是日期字符串
                if 时间字符串:
                    return 时间字符串
        
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def _提取内容(self, html: str) -> str:
        """从HTML中提取文章内容"""
        import re
        
        # 查找文章内容区域
        patterns = [
            r'<div[^>]*class="[^"]*rich_media_content[^"]*"[^>]*>(.*?)</div>',
            r'<div[^>]*id="js_content"[^>]*>(.*?)</div>',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE | re.DOTALL)
            if match:
                内容 = match.group(1)
                # 简单清理HTML标签，保留基本格式
                内容 = re.sub(r'<script[^>]*>.*?</script>', '', 内容, flags=re.DOTALL)
                内容 = re.sub(r'<style[^>]*>.*?</style>', '', 内容, flags=re.DOTALL)
                return 内容.strip()
        
        return "无法提取内容"
    
    def _检查文章时间(self, 发布时间: str) -> bool:
        """检查文章时间是否在采集范围内"""
        try:
            # 尝试解析时间
            if 发布时间.isdigit():
                文章时间 = datetime.fromtimestamp(int(发布时间))
            else:
                # 尝试多种时间格式
                时间格式 = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d',
                    '%Y年%m月%d日',
                    '%m月%d日',
                ]
                
                文章时间 = None
                for 格式 in 时间格式:
                    try:
                        文章时间 = datetime.strptime(发布时间, 格式)
                        break
                    except:
                        continue
                
                if not 文章时间:
                    return True  # 无法解析时间，默认采集
            
            return 文章时间 >= self.截止时间
            
        except:
            return True  # 解析失败，默认采集
    
    def _保存文章(self, 文章信息: Dict[str, Any]):
        """保存文章到文件"""
        try:
            # 生成文件名
            标题 = 文章信息['标题']
            # 清理文件名中的非法字符
            标题 = re.sub(r'[<>:"/\\|?*]', '_', 标题)
            文件名 = f"{标题[:50]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            文件路径 = os.path.join(self.保存目录, 文件名)
            
            # 保存文章
            with open(文件路径, 'w', encoding='utf-8') as f:
                f.write(f"标题: {文章信息['标题']}\n")
                f.write(f"作者: {文章信息['作者']}\n")
                f.write(f"发布时间: {文章信息['发布时间']}\n")
                f.write(f"来源: {文章信息['来源']}\n")
                f.write(f"链接: {文章信息['链接']}\n")
                f.write(f"采集时间: {文章信息['采集时间']}\n")
                f.write("=" * 50 + "\n")
                f.write(文章信息['内容'])
            
            print(f"💾 文章已保存: {文件名}")
            
        except Exception as 错误:
            print(f"❌ 保存文章失败: {错误}")
    
    def 使用说明(self):
        """显示使用说明"""
        print("\n📖 半自动微信采集器使用说明")
        print("=" * 50)
        print("🎯 工作原理:")
        print("   1. 程序监听系统剪贴板")
        print("   2. 您在微信中复制文章链接")
        print("   3. 程序自动识别并处理文章")
        print("   4. 自动保存符合时间条件的文章")
        print()
        print("📱 操作步骤:")
        print("   1. 运行本程序")
        print("   2. 在微信中打开公众号")
        print("   3. 点击文章，复制链接")
        print("   4. 程序自动处理并保存")
        print("   5. 重复步骤3-4处理更多文章")
        print()
        print("✨ 优势:")
        print("   - 无需复杂配置")
        print("   - 不依赖浏览器自动化")
        print("   - 稳定性高")
        print("   - 操作简单")
        print()
        print("⚠️ 注意事项:")
        print("   - 确保复制的是完整的文章链接")
        print("   - 程序会自动过滤时间范围")
        print("   - 按Ctrl+C可随时停止")


def 主函数():
    """主函数"""
    print("🚀 半自动微信采集器")
    print("=" * 60)
    
    # 检查依赖
    try:
        import pyperclip
        import requests
    except ImportError:
        print("❌ 缺少必要依赖，正在安装...")
        os.system("pip install pyperclip requests")
        print("✅ 依赖安装完成，请重新运行程序")
        return
    
    # 创建采集器
    采集器 = 半自动微信采集器(采集天数=2)
    
    # 显示使用说明
    采集器.使用说明()
    
    # 询问是否开始监听
    确认 = input("\n是否开始监听剪贴板？(y/n): ").strip().lower()
    if 确认 != 'y':
        print("👋 已取消监听")
        return
    
    print("\n🎧 开始监听剪贴板...")
    print("💡 现在可以在微信中复制文章链接了")
    
    try:
        # 开始监听
        文章列表 = 采集器.监听剪贴板(超时秒数=600)  # 10分钟超时
        
        # 显示结果
        if 文章列表:
            print(f"\n🎉 采集完成！")
            print(f"📊 共采集 {len(文章列表)} 篇文章")
            print(f"📁 保存位置: {采集器.保存目录}")
        else:
            print(f"\n📝 未采集到文章")
            print(f"💡 请确保复制的是微信文章链接")
    
    except Exception as 错误:
        print(f"\n❌ 采集过程中出现错误: {错误}")
    
    print(f"\n👋 程序结束")


if __name__ == "__main__":
    主函数()
