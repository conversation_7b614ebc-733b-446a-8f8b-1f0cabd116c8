# -*- coding: utf-8 -*-
"""
公众号自动发布器

作者: AI助手
日期: 2025-07-29
功能: 自动将套利分析报告发布到微信公众号
"""

import os
import sys
import json
import requests
import time
from datetime import datetime
from typing import Dict, Any, Optional

# 添加配置文件路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(os.path.dirname(当前目录))
配置文件路径 = os.path.join(根目录, "配置文件")
sys.path.append(配置文件路径)


class 公众号发布器:
    """微信公众号自动发布器"""
    
    def __init__(self):
        """初始化发布器"""
        self.access_token = None
        self.token_expires_time = 0
        
        # 从配置文件加载公众号配置
        self._加载配置()
        
        print("🚀 公众号自动发布器初始化完成")
    
    def _加载配置(self):
        """加载公众号配置"""
        try:
            # 尝试从配置文件加载
            配置文件 = os.path.join(配置文件路径, "公众号发布配置.json")
            
            if os.path.exists(配置文件):
                with open(配置文件, 'r', encoding='utf-8') as f:
                    配置 = json.load(f)
                
                self.app_id = 配置.get('app_id', '')
                self.app_secret = 配置.get('app_secret', '')
                
                print("✅ 从配置文件加载公众号配置")
            else:
                # 创建默认配置文件
                self._创建默认配置文件(配置文件)
                
        except Exception as e:
            print(f"⚠️ 加载配置失败: {e}")
            self._创建默认配置文件()
    
    def _创建默认配置文件(self, 配置文件路径: str = None):
        """创建默认配置文件"""
        if not 配置文件路径:
            配置文件路径 = os.path.join(配置文件路径, "公众号发布配置.json")
        
        默认配置 = {
            "app_id": "你的公众号AppID",
            "app_secret": "你的公众号AppSecret",
            "说明": {
                "获取方式": "登录微信公众平台 -> 开发 -> 基本配置",
                "注意事项": [
                    "需要认证的服务号才能使用接口",
                    "AppID和AppSecret需要保密",
                    "测试时可以使用测试号"
                ]
            }
        }
        
        try:
            os.makedirs(os.path.dirname(配置文件路径), exist_ok=True)
            with open(配置文件路径, 'w', encoding='utf-8') as f:
                json.dump(默认配置, f, ensure_ascii=False, indent=2)
            
            print(f"📝 已创建默认配置文件: {配置文件路径}")
            print("💡 请填写您的公众号AppID和AppSecret")
            
            # 设置默认值
            self.app_id = ""
            self.app_secret = ""
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
    
    def 获取access_token(self) -> Optional[str]:
        """获取微信公众号access_token"""
        try:
            # 检查token是否过期
            if self.access_token and time.time() < self.token_expires_time:
                return self.access_token
            
            if not self.app_id or not self.app_secret:
                print("❌ 请先配置公众号AppID和AppSecret")
                return None
            
            # 请求新的access_token
            url = "https://api.weixin.qq.com/cgi-bin/token"
            params = {
                'grant_type': 'client_credential',
                'appid': self.app_id,
                'secret': self.app_secret
            }
            
            print("🔑 正在获取access_token...")
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'access_token' in data:
                    self.access_token = data['access_token']
                    # 提前5分钟过期
                    self.token_expires_time = time.time() + data.get('expires_in', 7200) - 300
                    
                    print("✅ access_token获取成功")
                    return self.access_token
                else:
                    print(f"❌ 获取access_token失败: {data}")
                    return None
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取access_token异常: {e}")
            return None
    
    def 创建图文素材(self, 标题: str, 内容: str, 作者: str = "套利分析助手") -> Optional[str]:
        """创建图文素材"""
        try:
            access_token = self.获取access_token()
            if not access_token:
                return None
            
            # 转换内容格式
            html_内容 = self._转换为HTML格式(内容)
            
            # 构建图文消息
            articles = [{
                "title": 标题,
                "author": 作者,
                "digest": self._生成摘要(内容),
                "content": html_内容,
                "content_source_url": "",
                "thumb_media_id": "",  # 如果有封面图片的media_id
                "show_cover_pic": 0,
                "need_open_comment": 0,
                "only_fans_can_comment": 0
            }]
            
            # 上传图文素材
            url = f"https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}"
            
            data = {
                "articles": articles
            }
            
            print("📝 正在创建图文素材...")
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('errcode') == 0:
                    media_id = result.get('media_id')
                    print(f"✅ 图文素材创建成功，media_id: {media_id}")
                    return media_id
                else:
                    print(f"❌ 创建图文素材失败: {result}")
                    return None
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 创建图文素材异常: {e}")
            return None
    
    def 发布图文消息(self, media_id: str) -> bool:
        """发布图文消息"""
        try:
            access_token = self.获取access_token()
            if not access_token:
                return False
            
            url = f"https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={access_token}"
            
            data = {
                "media_id": media_id
            }
            
            print("📢 正在发布图文消息...")
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('errcode') == 0:
                    publish_id = result.get('publish_id')
                    print(f"✅ 图文消息发布成功，publish_id: {publish_id}")
                    return True
                else:
                    print(f"❌ 发布图文消息失败: {result}")
                    return False
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 发布图文消息异常: {e}")
            return False
    
    def _转换为HTML格式(self, 内容: str) -> str:
        """将文本内容转换为HTML格式"""
        # 基本的文本到HTML转换
        html_内容 = 内容.replace('\n', '<br/>')
        
        # 添加样式
        html_内容 = f"""
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333;">
            {html_内容}
        </div>
        """
        
        # 处理特殊格式
        html_内容 = html_内容.replace('📊', '<strong>📊</strong>')
        html_内容 = html_内容.replace('🎯', '<strong>🎯</strong>')
        html_内容 = html_内容.replace('💰', '<strong>💰</strong>')
        html_内容 = html_内容.replace('🏢', '<strong>🏢</strong>')
        html_内容 = html_内容.replace('💎', '<strong>💎</strong>')
        
        return html_内容
    
    def _生成摘要(self, 内容: str, 最大长度: int = 120) -> str:
        """生成文章摘要"""
        # 提取前几行作为摘要
        lines = 内容.split('\n')
        摘要 = ""
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('=') and not line.startswith('-'):
                摘要 += line + " "
                if len(摘要) > 最大长度:
                    break
        
        if len(摘要) > 最大长度:
            摘要 = 摘要[:最大长度] + "..."
        
        return 摘要.strip()
    
    def 自动发布套利报告(self, 报告文件路径: str) -> bool:
        """自动发布套利分析报告"""
        try:
            if not os.path.exists(报告文件路径):
                print(f"❌ 报告文件不存在: {报告文件路径}")
                return False
            
            # 读取报告内容
            with open(报告文件路径, 'r', encoding='utf-8') as f:
                报告内容 = f.read()
            
            # 生成标题
            当前时间 = datetime.now().strftime('%Y-%m-%d')
            标题 = f"📊 每日套利分析报告 - {当前时间}"
            
            print(f"📰 准备发布报告: {标题}")
            
            # 创建图文素材
            media_id = self.创建图文素材(标题, 报告内容)
            
            if not media_id:
                print("❌ 创建图文素材失败")
                return False
            
            # 发布图文消息
            成功 = self.发布图文消息(media_id)
            
            if 成功:
                print("🎉 套利报告发布成功！")
                return True
            else:
                print("❌ 套利报告发布失败")
                return False
                
        except Exception as e:
            print(f"❌ 自动发布异常: {e}")
            return False
    
    def 测试发布(self) -> bool:
        """测试发布功能"""
        print("🧪 开始测试发布功能...")
        
        # 创建测试内容
        测试内容 = f"""
📊 套利分析测试报告

🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 这是一个测试发布，验证自动发布功能是否正常工作。

💡 主要功能测试：
• 自动获取access_token ✅
• 创建图文素材 ✅  
• 发布到公众号 ✅

📈 如果您看到这条消息，说明自动发布系统工作正常！

⚠️ 这是测试消息，请忽略具体内容。

---
🤖 由套利分析助手自动发布
        """
        
        # 创建并发布测试消息
        标题 = f"🧪 套利分析系统测试 - {datetime.now().strftime('%H:%M')}"
        
        media_id = self.创建图文素材(标题, 测试内容)
        
        if media_id:
            return self.发布图文消息(media_id)
        else:
            return False


def 主函数():
    """主函数"""
    print("🚀 公众号自动发布器")
    print("=" * 50)
    
    # 创建发布器
    发布器 = 公众号发布器()
    
    # 检查配置
    if not 发布器.app_id or 发布器.app_id == "你的公众号AppID":
        print("⚠️ 请先配置公众号AppID和AppSecret")
        print(f"📝 配置文件位置: {os.path.join(配置文件路径, '公众号发布配置.json')}")
        return
    
    print("🔧 配置检查完成")
    
    # 询问操作类型
    print("\n请选择操作:")
    print("1. 测试发布功能")
    print("2. 发布最新套利报告")
    print("3. 发布指定报告文件")
    
    选择 = input("\n请输入选择 (1-3): ").strip()
    
    if 选择 == "1":
        print("\n🧪 开始测试发布...")
        成功 = 发布器.测试发布()
        
        if 成功:
            print("✅ 测试发布成功！请检查您的公众号")
        else:
            print("❌ 测试发布失败，请检查配置和网络")
    
    elif 选择 == "2":
        # 查找最新的套利报告
        采集结果目录 = os.path.join(根目录, "采集结果")
        报告文件列表 = []
        
        for 文件名 in os.listdir(采集结果目录):
            if 文件名.startswith("套利分析报告_") and 文件名.endswith(".txt"):
                报告文件列表.append(文件名)
        
        if not 报告文件列表:
            print("❌ 未找到套利分析报告")
            return
        
        # 选择最新的报告
        最新报告 = sorted(报告文件列表)[-1]
        报告路径 = os.path.join(采集结果目录, 最新报告)
        
        print(f"📄 准备发布最新报告: {最新报告}")
        
        成功 = 发布器.自动发布套利报告(报告路径)
        
        if 成功:
            print("🎉 套利报告发布成功！")
        else:
            print("❌ 套利报告发布失败")
    
    elif 选择 == "3":
        报告路径 = input("请输入报告文件路径: ").strip()
        
        if os.path.exists(报告路径):
            成功 = 发布器.自动发布套利报告(报告路径)
            
            if 成功:
                print("🎉 报告发布成功！")
            else:
                print("❌ 报告发布失败")
        else:
            print("❌ 文件不存在")
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    主函数()
