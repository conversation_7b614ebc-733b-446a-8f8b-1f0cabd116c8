# -*- coding: utf-8 -*-
"""
链接管理工具 - 管理公众号文章链接

作者: AI助手
日期: 2025-07-29
功能: 添加、检查和管理公众号文章链接
"""

import os
import sys
from datetime import datetime, timedelta

# 添加配置文件路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(os.path.dirname(当前目录))
配置文件路径 = os.path.join(根目录, "配置文件")
sys.path.append(配置文件路径)

from 简化文章采集器 import 简化文章采集器


class 链接管理工具:
    """链接管理工具"""
    
    def __init__(self):
        self.采集器 = 简化文章采集器(采集天数=2)
    
    def 添加文章链接(self, 公众号名称: str, 文章链接: str):
        """
        添加文章链接并检查时间
        
        参数:
            公众号名称: 公众号名称
            文章链接: 文章链接
        """
        print(f"🔍 正在检查文章链接: {文章链接}")
        
        # 采集文章获取详细信息
        文章数据 = self.采集器.采集文章(文章链接, 公众号名称)
        
        if not 文章数据:
            print("❌ 无法采集文章信息")
            return False
        
        标题 = 文章数据.get('标题', '未知标题')
        发布时间 = 文章数据.get('发布时间', '')
        字数 = 文章数据.get('字数', 0)
        
        print(f"📝 文章标题: {标题}")
        print(f"🕒 发布时间: {发布时间}")
        print(f"📊 字数: {字数}")
        
        # 检查时间是否符合条件
        if self.采集器._检查文章时间(发布时间):
            print("✅ 文章符合时间条件（最近2天）")
            
            # 保存文章
            文件路径 = self.采集器.保存文章到文件(文章数据)
            if 文件路径:
                print(f"💾 文章已保存: {os.path.basename(文件路径)}")
                return True
            else:
                print("❌ 文章保存失败")
                return False
        else:
            print("⏰ 文章不符合时间条件（超过2天）")
            return False
    
    def 批量添加链接(self, 链接列表: list):
        """
        批量添加链接
        
        参数:
            链接列表: [{'公众号': '名称', '链接': 'url'}, ...]
        """
        print(f"🚀 开始批量处理 {len(链接列表)} 个链接")
        
        成功数量 = 0
        失败数量 = 0
        
        for i, 链接信息 in enumerate(链接列表, 1):
            print(f"\n📖 处理第 {i}/{len(链接列表)} 个链接")
            
            公众号名称 = 链接信息.get('公众号', '未知')
            文章链接 = 链接信息.get('链接', '')
            
            if self.添加文章链接(公众号名称, 文章链接):
                成功数量 += 1
            else:
                失败数量 += 1
            
            # 添加延迟
            if i < len(链接列表):
                print("⏳ 等待 2 秒...")
                import time
                time.sleep(2)
        
        print(f"\n🎉 批量处理完成！")
        print(f"✅ 成功: {成功数量} 篇")
        print(f"❌ 失败: {失败数量} 篇")
    
    def 检查现有链接(self):
        """检查配置文件中现有链接的时间"""
        print("🔍 检查配置文件中现有链接的时间...")
        
        配置信息 = self.采集器.获取公众号配置信息()
        公众号配置 = 配置信息['公众号配置']
        
        for 账号ID, 信息 in 公众号配置.items():
            if not 信息.get('是否启用', False):
                continue
            
            公众号名称 = 信息['名称']
            示例链接 = 信息.get('示例链接', [])
            
            if not 示例链接:
                print(f"⚠️ {公众号名称}: 没有示例链接")
                continue
            
            print(f"\n📋 检查 {公众号名称} 的链接:")
            
            for 链接 in 示例链接:
                print(f"🔗 {链接}")
                文章数据 = self.采集器.采集文章(链接, 公众号名称)
                
                if 文章数据:
                    发布时间 = 文章数据.get('发布时间', '')
                    标题 = 文章数据.get('标题', '未知标题')
                    
                    if self.采集器._检查文章时间(发布时间):
                        print(f"   ✅ {标题[:30]}... ({发布时间}) - 符合条件")
                    else:
                        print(f"   ⏰ {标题[:30]}... ({发布时间}) - 超出时间范围")
                else:
                    print(f"   ❌ 无法采集文章信息")


def 主函数():
    """主函数"""
    print("🔧 链接管理工具")
    print("=" * 50)
    
    工具 = 链接管理工具()
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 添加单个文章链接")
        print("2. 批量添加文章链接")
        print("3. 检查现有链接时间")
        print("4. 退出")
        
        选择 = input("\n请输入选择 (1-4): ").strip()
        
        if 选择 == '1':
            公众号名称 = input("请输入公众号名称: ").strip()
            文章链接 = input("请输入文章链接: ").strip()
            
            if 公众号名称 and 文章链接:
                工具.添加文章链接(公众号名称, 文章链接)
            else:
                print("❌ 请输入有效的公众号名称和文章链接")
        
        elif 选择 == '2':
            print("📝 请输入文章链接，格式：公众号名称|文章链接")
            print("   例如：饕餮海投资|https://mp.weixin.qq.com/s/...")
            print("   输入空行结束")
            
            链接列表 = []
            while True:
                输入 = input("请输入: ").strip()
                if not 输入:
                    break
                
                if '|' in 输入:
                    公众号名称, 文章链接 = 输入.split('|', 1)
                    链接列表.append({
                        '公众号': 公众号名称.strip(),
                        '链接': 文章链接.strip()
                    })
                else:
                    print("❌ 格式错误，请使用：公众号名称|文章链接")
            
            if 链接列表:
                工具.批量添加链接(链接列表)
            else:
                print("❌ 没有输入有效的链接")
        
        elif 选择 == '3':
            工具.检查现有链接()
        
        elif 选择 == '4':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请输入 1-4")


if __name__ == "__main__":
    主函数()
