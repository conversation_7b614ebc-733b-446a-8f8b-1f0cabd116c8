# -*- coding: utf-8 -*-
"""
全自动采集器使用示例

作者: AI助手
日期: 2025-07-29
功能: 演示如何使用全自动采集器
"""

import os
import sys
from datetime import datetime

# 添加当前目录到路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
sys.path.append(当前目录)

from 简化文章采集器 import 简化文章采集器


def 示例1_基本使用():
    """示例1：基本使用方法"""
    print("📖 示例1：基本使用方法")
    print("=" * 40)
    
    # 创建采集器，采集最近2天的文章
    采集器 = 简化文章采集器(采集天数=2)
    
    # 显示配置信息
    配置信息 = 采集器.获取公众号配置信息()
    print(f"📊 采集天数: {配置信息['采集天数']} 天")
    print(f"📅 截止时间: {配置信息['截止时间']}")
    print(f"📁 保存目录: {采集器.保存目录}")
    
    # 从配置文件自动采集
    保存文件列表 = 采集器.从配置文件采集()
    
    if 保存文件列表:
        print(f"\n✅ 成功采集 {len(保存文件列表)} 篇文章")
        for 文件路径 in 保存文件列表:
            print(f"   📄 {os.path.basename(文件路径)}")
    else:
        print("\n❌ 没有采集到符合条件的文章")


def 示例2_单篇文章采集():
    """示例2：单篇文章采集和时间检查"""
    print("\n📖 示例2：单篇文章采集和时间检查")
    print("=" * 40)
    
    采集器 = 简化文章采集器(采集天数=2)
    
    # 测试链接（这是一个示例链接）
    测试链接 = "https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ"
    
    print(f"🔍 测试链接: {测试链接}")
    
    # 采集文章
    文章数据 = 采集器.采集文章(测试链接, "饕餮海投资")
    
    if 文章数据:
        print(f"📝 标题: {文章数据['标题']}")
        print(f"👤 作者: {文章数据['作者']}")
        print(f"🕒 发布时间: {文章数据['发布时间']}")
        print(f"📊 字数: {文章数据['字数']}")
        
        # 检查时间是否符合条件
        if 采集器._检查文章时间(文章数据['发布时间']):
            print("✅ 文章符合时间条件")
            
            # 保存文章
            文件路径 = 采集器.保存文章到文件(文章数据)
            if 文件路径:
                print(f"💾 文章已保存: {os.path.basename(文件路径)}")
        else:
            print("⏰ 文章不符合时间条件")
    else:
        print("❌ 文章采集失败")


def 示例3_批量采集():
    """示例3：批量采集多个链接"""
    print("\n📖 示例3：批量采集多个链接")
    print("=" * 40)
    
    采集器 = 简化文章采集器(采集天数=2)
    
    # 准备测试链接列表
    测试链接列表 = [
        {
            '链接': 'https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ',
            '来源账号': '饕餮海投资'
        },
        # 可以添加更多链接
    ]
    
    print(f"🚀 准备批量采集 {len(测试链接列表)} 篇文章")
    
    # 批量采集
    保存文件列表 = 采集器.批量采集文章(测试链接列表)
    
    print(f"\n📊 批量采集结果:")
    print(f"   总链接数: {len(测试链接列表)}")
    print(f"   成功保存: {len(保存文件列表)}")
    
    for 文件路径 in 保存文件列表:
        print(f"   📄 {os.path.basename(文件路径)}")


def 示例4_时间过滤测试():
    """示例4：时间过滤功能测试"""
    print("\n📖 示例4：时间过滤功能测试")
    print("=" * 40)
    
    采集器 = 简化文章采集器(采集天数=2)
    
    # 测试不同的时间格式
    测试时间列表 = [
        datetime.now().strftime('%Y-%m-%d'),  # 今天
        (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),  # 昨天
        (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d'),  # 3天前
        '2025-07-29',  # 具体日期
        '2025年07月29日',  # 中文格式
        '07月29日',  # 简化格式
        '',  # 空字符串
    ]
    
    print("🧪 测试时间过滤功能:")
    for 时间文本 in 测试时间列表:
        结果 = 采集器._检查文章时间(时间文本)
        状态 = "✅ 通过" if 结果 else "❌ 过滤"
        print(f"   {时间文本 or '(空)':15} -> {状态}")


def 示例5_配置信息查看():
    """示例5：查看配置信息"""
    print("\n📖 示例5：查看配置信息")
    print("=" * 40)
    
    采集器 = 简化文章采集器(采集天数=2)
    配置信息 = 采集器.获取公众号配置信息()
    
    print("📋 公众号配置:")
    for 账号ID, 信息 in 配置信息['公众号配置'].items():
        print(f"   🔸 {信息['名称']} ({账号ID})")
        print(f"      微信号: {信息.get('微信号', '未设置')}")
        print(f"      描述: {信息['描述']}")
        print(f"      采集频率: {信息['采集频率']}")
        print(f"      示例链接数: {len(信息.get('示例链接', []))}")
        print()
    
    print(f"⚙️ 采集器配置:")
    print(f"   采集天数: {配置信息['采集天数']} 天")
    print(f"   截止时间: {配置信息['截止时间']}")


def 主函数():
    """主函数"""
    print("🚀 全自动采集器使用示例")
    print("=" * 60)
    
    try:
        # 运行所有示例
        示例1_基本使用()
        示例2_单篇文章采集()
        示例3_批量采集()
        
        # 导入timedelta用于时间测试
        from datetime import timedelta
        示例4_时间过滤测试()
        
        示例5_配置信息查看()
        
        print(f"\n🎉 所有示例运行完成！")
        print(f"💡 提示：")
        print(f"   1. 采集器会自动过滤超过2天的文章")
        print(f"   2. 文章保存在 '数据存储/原文章' 目录")
        print(f"   3. 可以通过修改配置文件添加更多公众号")
        print(f"   4. 使用 '链接管理工具.py' 可以方便地添加新链接")
        
    except Exception as 错误:
        print(f"❌ 运行示例时出现错误: {错误}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    主函数()
