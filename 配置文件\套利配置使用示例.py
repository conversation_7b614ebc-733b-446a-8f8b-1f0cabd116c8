#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
套利信息提取配置使用示例
演示如何使用套利信息提取配置文件

作者: AI助手
日期: 2025-07-29
"""

import sys
import os

# 添加配置文件路径
当前目录 = os.path.dirname(__file__)
sys.path.append(当前目录)

# 导入套利配置
from 套利信息提取配置 import (
    获取配置, 生成文件名, 构建提取提示词, 构建总结提示词,
    套利配置
)

def 演示基本配置获取():
    """演示基本配置获取"""
    print("📋 基本配置获取演示")
    print("-" * 40)
    
    # 获取基本设置
    基本设置 = 获取配置('基本设置')
    print(f"报告语言: {基本设置.get('报告语言')}")
    print(f"输出格式: {基本设置.get('输出格式')}")
    print(f"保存路径: {基本设置.get('保存路径')}")
    
    # 获取字数限制
    字数限制 = 获取配置('字数限制')
    print(f"最小字数: {字数限制.get('最小字数')}")
    print(f"最大字数: {字数限制.get('最大字数')}")
    print(f"警告阈值: {字数限制.get('警告阈值')}")
    
    print()

def 演示提取规则获取():
    """演示提取规则获取"""
    print("🔍 提取规则获取演示")
    print("-" * 40)
    
    # 获取核心提取内容
    核心内容 = 套利配置.获取核心提取内容()
    print("核心提取内容:")
    for i, 内容 in enumerate(核心内容, 1):
        print(f"  {i}. {内容}")
    
    # 获取重点关注配置
    重点关注 = 套利配置.获取重点关注配置()
    print("\n重点关注配置:")
    for 类别, 项目列表 in 重点关注.items():
        print(f"  {类别}:")
        for 项目 in 项目列表:
            print(f"    - {项目}")
    
    print()

def 演示风险和禁止内容():
    """演示风险提示和禁止内容规则"""
    print("⚠️ 风险提示和禁止内容演示")
    print("-" * 40)
    
    # 获取标准风险提示
    标准风险提示 = 套利配置.获取标准风险提示()
    print("标准风险提示:")
    for i, 提示 in enumerate(标准风险提示, 1):
        print(f"  {i}. {提示}")
    
    # 获取禁止表述
    禁止表述 = 套利配置.获取禁止表述()
    print(f"\n禁止表述 ({len(禁止表述)}个):")
    for 表述 in 禁止表述:
        print(f"  ❌ {表述}")
    
    # 获取限制词汇
    限制词汇 = 套利配置.获取限制词汇()
    print(f"\n限制词汇 ({len(限制词汇)}个):")
    for 词汇 in 限制词汇:
        print(f"  ⚠️ {词汇}")
    
    print()

def 演示文件名生成():
    """演示文件名生成"""
    print("📁 文件名生成演示")
    print("-" * 40)
    
    # 使用默认前缀生成文件名
    默认文件名 = 生成文件名()
    print(f"默认文件名: {默认文件名}")
    
    # 使用自定义前缀生成文件名
    自定义文件名 = 生成文件名("测试套利报告")
    print(f"自定义文件名: {自定义文件名}")
    
    # 获取文件命名规则
    命名规则 = 套利配置.获取文件命名规则()
    print(f"命名规则: {命名规则}")
    
    print()

def 演示AI提示词构建():
    """演示AI提示词构建"""
    print("🤖 AI提示词构建演示")
    print("-" * 40)
    
    # 模拟文章数据
    测试标题 = "A股港股价差套利机会分析"
    测试作者 = "投资分析师"
    测试内容 = """
    近期A股和港股市场出现明显价差，为投资者提供了套利机会。
    以某科技股为例，A股价格为100元，港股价格为80港币（约合人民币72元），
    价差达到28%，存在明显的套利空间。
    
    操作策略：
    1. 买入港股，卖出A股
    2. 等待价差收敛
    3. 预期收益率15-20%
    
    风险提示：
    - 汇率波动风险
    - 市场流动性风险
    - 政策变化风险
    """
    
    # 构建提取提示词
    提取提示词 = 构建提取提示词(测试标题, 测试作者, 测试内容)
    print("提取提示词预览（前500字符）:")
    print(提取提示词[:500] + "...")
    
    # 构建总结提示词
    模拟套利信息 = "发现A股港股价差套利机会，预期收益15-20%，存在汇率和流动性风险。"
    总结提示词 = 构建总结提示词(模拟套利信息)
    print(f"\n总结提示词预览（前300字符）:")
    print(总结提示词[:300] + "...")
    
    print()

def 演示合规要求():
    """演示合规要求"""
    print("📜 合规要求演示")
    print("-" * 40)
    
    # 获取免责声明
    免责声明 = 套利配置.获取免责声明()
    print("免责声明:")
    print(免责声明)
    
    # 获取合规要求
    合规要求 = 获取配置('合规要求')
    信息来源要求 = 合规要求.get('信息来源', {})
    print(f"\n信息来源要求: {信息来源要求.get('要求')}")
    print(f"信息来源格式: {信息来源要求.get('格式')}")
    
    作者信息 = 合规要求.get('作者信息', {})
    print(f"作者署名: {作者信息.get('署名')}")
    print(f"生成时间要求: {作者信息.get('生成时间')}")
    
    print()

def 演示自定义配置():
    """演示自定义配置"""
    print("🎨 自定义配置演示")
    print("-" * 40)
    
    # 获取用户自定义配置
    用户自定义 = 套利配置.获取用户自定义配置()
    print("用户自定义配置:")
    for 配置项, 配置值 in 用户自定义.items():
        print(f"  {配置项}: {配置值}")
    
    # 获取个性化设置
    个性化设置 = 套利配置.获取个性化设置()
    print("\n个性化设置:")
    for 设置项, 设置值 in 个性化设置.items():
        print(f"  {设置项}: {设置值}")
    
    print()

def 演示配置修改():
    """演示如何修改配置（仅演示，不实际保存）"""
    print("✏️ 配置修改演示")
    print("-" * 40)
    
    print("当前字数限制:")
    字数限制 = 套利配置.获取字数限制()
    for 项目, 数值 in 字数限制.items():
        print(f"  {项目}: {数值}")
    
    print("\n💡 修改配置的方法:")
    print("1. 直接编辑 '套利信息提取规则.yaml' 文件")
    print("2. 修改对应的配置项数值")
    print("3. 保存文件后重新加载配置")
    print("4. 或者通过代码动态修改:")
    
    print("\n示例代码:")
    print("```python")
    print("# 修改字数限制")
    print("套利配置.配置数据['文章生成限制']['字数控制']['最大字数'] = 3000")
    print("# 保存配置")
    print("套利配置.保存配置()")
    print("```")
    
    print()

def 主函数():
    """主函数 - 运行所有演示"""
    print("🎯 套利信息提取配置使用示例")
    print("=" * 60)
    
    # 验证配置完整性
    if not 套利配置.验证配置完整性():
        print("❌ 配置验证失败，请检查配置文件")
        return
    
    print("✅ 配置验证通过，开始演示\n")
    
    # 运行各个演示
    演示基本配置获取()
    演示提取规则获取()
    演示风险和禁止内容()
    演示文件名生成()
    演示AI提示词构建()
    演示合规要求()
    演示自定义配置()
    演示配置修改()
    
    print("🎉 所有演示完成！")
    print("=" * 60)
    print("💡 提示：")
    print("- 可以直接编辑 '套利信息提取规则.yaml' 文件来修改配置")
    print("- 修改后重新运行程序即可生效")
    print("- 建议在修改前备份原配置文件")

if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
