# -*- coding: utf-8 -*-
"""
简化文章采集器 - 整合所有采集功能

作者: AI助手
日期: 2025-07-27
功能: 简化的公众号文章采集工具，支持真实文章采集和文件保存
"""

import os
import re
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager


class 简化文章采集器:
    """简化的文章采集器"""
    
    def __init__(self, 保存目录: str = None, 采集天数: int = 2):
        """
        初始化采集器

        参数:
            保存目录: 文章保存目录，如果为None则使用默认目录
            采集天数: 采集最近几天的文章，默认2天
        """
        # 导入配置文件
        self._导入配置文件()

        if 保存目录 is None:
            # 从配置文件获取保存目录
            保存目录 = self.采集器配置.get('保存目录', '数据存储/原文章')
            当前文件目录 = os.path.dirname(os.path.abspath(__file__))
            根目录 = os.path.dirname(os.path.dirname(当前文件目录))
            self.保存目录 = os.path.join(根目录, 保存目录)
        else:
            self.保存目录 = 保存目录

        self.采集天数 = 采集天数
        self.截止时间 = datetime.now() - timedelta(days=采集天数)

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self._创建保存目录()

        print(f"🕒 采集器初始化完成，将采集最近 {采集天数} 天的文章")
        print(f"📅 截止时间: {self.截止时间.strftime('%Y-%m-%d %H:%M:%S')}")

    def _导入配置文件(self):
        """导入配置文件"""
        try:
            当前文件目录 = os.path.dirname(os.path.abspath(__file__))
            根目录 = os.path.dirname(os.path.dirname(当前文件目录))
            配置文件路径 = os.path.join(根目录, "配置文件")
            sys.path.append(配置文件路径)

            from 公众号配置 import 获取公众号列表, 获取采集配置, 获取文件保存配置

            self.公众号配置 = 获取公众号列表(只显示启用=True)
            self.采集器配置 = 获取采集配置()
            self.文件保存配置 = 获取文件保存配置()

            print("✅ 配置文件导入成功")

        except Exception as 错误:
            print(f"❌ 导入配置文件失败: {错误}")
            # 使用默认配置
            self.公众号配置 = {}
            self.采集器配置 = {'保存目录': '数据存储/原文章'}
            self.文件保存配置 = {}

    def _创建保存目录(self):
        """创建文章保存目录"""
        try:
            if not os.path.exists(self.保存目录):
                os.makedirs(self.保存目录)
                print(f"✅ 创建文章保存目录: {self.保存目录}")
        except Exception as 错误:
            print(f"❌ 创建保存目录失败: {错误}")

    def _提取发布时间(self, soup) -> str:
        """
        从网页中提取发布时间

        参数:
            soup: BeautifulSoup对象

        返回:
            发布时间字符串
        """
        import re

        # 首先尝试从页面源码中用正则表达式提取时间戳
        try:
            页面源码 = str(soup)

            # 微信文章常见的时间模式
            时间模式列表 = [
                # JavaScript变量中的时间戳
                r'ct\s*=\s*(\d{10})',  # 微信文章常用的ct变量
                r'createTime\s*=\s*(\d{10})',
                r'publish_time\s*=\s*(\d{10})',
                # 直接的时间格式
                r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2})',
                r'(\d{4}年\d{1,2}月\d{1,2}日)',
                r'(\d{1,2}月\d{1,2}日)',
            ]

            for 模式 in 时间模式列表:
                匹配结果 = re.search(模式, 页面源码)
                if 匹配结果:
                    时间文本 = 匹配结果.group(1)

                    # 如果是时间戳，转换为日期格式
                    if 时间文本.isdigit() and len(时间文本) == 10:
                        try:
                            时间戳 = int(时间文本)
                            日期时间 = datetime.fromtimestamp(时间戳)
                            时间文本 = 日期时间.strftime('%Y-%m-%d')
                            print(f"🔍 通过时间戳找到时间: {时间文本}")
                            return 时间文本
                        except:
                            continue
                    else:
                        print(f"🔍 通过正则表达式找到时间: {时间文本}")
                        return 时间文本
        except:
            pass

        # 然后尝试HTML元素选择器
        时间选择器列表 = [
            ('em', {'id': 'publish_time'}),
            ('span', {'class': 'rich_media_meta_text'}),
            ('div', {'class': 'rich_media_meta_list'}),
            ('span', {'id': 'post-date'}),
            ('time', {}),
        ]

        for 标签, 属性 in 时间选择器列表:
            try:
                if 属性:
                    elem = soup.find(标签, 属性)
                else:
                    elem = soup.find(标签)

                if elem:
                    时间文本 = elem.get_text().strip()
                    # 检查是否真的是时间格式
                    if 时间文本 and (
                        re.match(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', 时间文本) or
                        re.match(r'\d{1,2}月\d{1,2}日', 时间文本) or
                        re.match(r'\d{4}-\d{1,2}-\d{1,2}', 时间文本)
                    ):
                        print(f"🔍 通过 {标签}[{属性}] 找到时间: {时间文本}")
                        return 时间文本
            except:
                continue

        print("⚠️ 无法提取发布时间")
        return ""

    def 获取公众号文章列表(self, 公众号名称: str, 公众号微信号: str = None) -> List[Dict[str, str]]:
        """
        通过搜狗微信搜索获取公众号最近文章（基于Selenium）

        参数:
            公众号名称: 公众号名称
            公众号微信号: 公众号微信号（可选）

        返回:
            文章列表 [{'标题': '', '链接': '', '发布时间': '', '摘要': '', '来源': ''}, ...]
        """
        print(f"🔍 正在通过搜狗微信搜索获取 '{公众号名称}' 的最近文章...")

        # 使用Selenium搜狗微信搜索
        文章列表 = self._通过搜狗微信Selenium获取文章(公众号名称, 公众号微信号)

        if 文章列表:
            print(f"✅ 搜狗微信搜索获取到 {len(文章列表)} 篇文章")
            # 按时间过滤最近2天的文章
            符合条件的文章 = []
            for 文章 in 文章列表:
                if self._检查文章时间(文章['发布时间']):
                    符合条件的文章.append(文章)

            print(f"📅 时间过滤后剩余 {len(符合条件的文章)} 篇文章")
            return 符合条件的文章
        else:
            print("❌ 搜狗微信搜索未获取到文章")
            return []

    def _通过搜狗微信Selenium获取文章(self, 公众号名称: str, 公众号微信号: str = None) -> List[Dict[str, str]]:
        """
        使用Selenium通过搜狗微信搜索获取文章

        参数:
            公众号名称: 公众号名称
            公众号微信号: 公众号微信号（可选）

        返回:
            文章列表
        """
        driver = None
        try:
            # 配置Chrome选项
            options = Options()
            # 不加载图片，提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                'permissions.default.stylesheet': 2
            }
            options.add_experimental_option("prefs", prefs)
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 启动Chrome浏览器（自动管理ChromeDriver）
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            print("🌐 打开搜狗微信搜索页面...")
            driver.get("https://weixin.sogou.com/")
            driver.implicitly_wait(10)

            # 构建搜索关键词
            if 公众号微信号:
                搜索关键词 = f"{公众号名称} {公众号微信号}"
            else:
                搜索关键词 = 公众号名称

            print(f"🔍 搜索关键词: {搜索关键词}")

            # 输入搜索关键词
            搜索框 = driver.find_element(By.CLASS_NAME, "sec-input")
            搜索框.clear()
            搜索框.send_keys(搜索关键词)

            # 点击搜文章按钮
            搜索按钮 = driver.find_element(By.CLASS_NAME, "enter-input.article")
            搜索按钮.click()

            print("⏳ 等待搜索结果加载...")
            time.sleep(3)

            # 检查是否需要验证码
            if self._检查验证码(driver):
                print("🔐 检测到验证码，请手动处理...")
                input("请在浏览器中完成验证码验证，然后按回车继续...")

            # 获取文章列表
            文章列表 = self._解析搜狗搜索结果(driver, 公众号名称)

            return 文章列表

        except Exception as 错误:
            print(f"❌ Selenium搜狗微信搜索失败: {错误}")
            return []
        finally:
            if driver:
                driver.quit()

    def _检查验证码(self, driver) -> bool:
        """检查是否出现验证码"""
        try:
            # 查找验证码相关元素
            验证码元素 = driver.find_elements(By.CLASS_NAME, "p4")
            return len(验证码元素) > 0
        except:
            return False

    def _解析搜狗搜索结果(self, driver, 公众号名称: str) -> List[Dict[str, str]]:
        """
        解析搜狗搜索结果页面

        参数:
            driver: Selenium WebDriver
            公众号名称: 公众号名称

        返回:
            文章列表
        """
        文章列表 = []

        try:
            # 判断搜索结果页数
            try:
                分页元素 = driver.find_element(By.CLASS_NAME, 'p-fy')
                页数 = len(分页元素.find_elements(By.XPATH, 'a'))
            except:
                页数 = 1

            print(f"📄 搜索结果共 {页数} 页")

            # 遍历每一页
            for 页码 in range(页数):
                print(f"📖 正在处理第 {页码 + 1} 页...")

                # 获取当前页的文章列表
                try:
                    新闻列表 = driver.find_element(By.CLASS_NAME, 'news-list')
                    文章元素列表 = 新闻列表.find_elements(By.XPATH, 'li')

                    print(f"   找到 {len(文章元素列表)} 篇文章")

                    for 文章元素 in 文章元素列表:
                        try:
                            # 提取文章信息
                            文本框 = 文章元素.find_element(By.CLASS_NAME, 'txt-box')

                            # 提取发布时间
                            时间元素 = 文本框.find_element(By.CLASS_NAME, "s2")
                            发布时间 = 时间元素.text.strip()

                            # 检查时间是否符合条件（在这里先检查，避免无用的数据提取）
                            if not self._检查文章时间(发布时间):
                                continue

                            # 提取标题
                            标题元素 = 文本框.find_element(By.XPATH, 'h3')
                            标题 = 标题元素.get_attribute('textContent').strip()

                            # 提取链接
                            链接元素 = 文本框.find_element(By.TAG_NAME, 'a')
                            链接 = 链接元素.get_attribute('href')

                            # 提取摘要
                            try:
                                摘要元素 = 文本框.find_element(By.CLASS_NAME, 'txt-info')
                                摘要 = 摘要元素.get_attribute('textContent').strip()
                            except:
                                摘要 = ""

                            if 标题 and 链接:
                                文章信息 = {
                                    '标题': 标题,
                                    '链接': 链接,
                                    '发布时间': 发布时间,
                                    '摘要': 摘要,
                                    '来源': '搜狗微信',
                                    '来源账号': 公众号名称
                                }
                                文章列表.append(文章信息)
                                print(f"   ✅ 采集文章: {标题[:30]}... ({发布时间})")

                        except Exception as e:
                            continue

                    # 点击下一页（如果不是最后一页）
                    if 页码 < 页数 - 1:
                        try:
                            下一页按钮 = driver.find_element(By.CLASS_NAME, 'np')
                            下一页按钮.click()
                            time.sleep(2)  # 等待页面加载
                        except:
                            print("   ⚠️ 无法点击下一页，可能已到最后一页")
                            break

                except Exception as e:
                    print(f"   ❌ 处理第 {页码 + 1} 页时出错: {e}")
                    continue

            return 文章列表

        except Exception as 错误:
            print(f"❌ 解析搜索结果失败: {错误}")
            return []

    def _获取微信API参数(self, 公众号名称: str) -> Dict[str, str]:
        """
        从配置文件获取微信API参数

        返回:
            包含biz, uin, key等参数的字典
        """
        try:
            # 从配置文件中查找对应公众号的微信API参数
            for 账号ID, 配置信息 in self.公众号配置.items():
                if 配置信息.get('名称') == 公众号名称:
                    微信API参数 = 配置信息.get('微信API参数', {})
                    if 微信API参数 and all(key in 微信API参数 for key in ['biz', 'uin', 'key']):
                        return 微信API参数

            print("⚠️ 未找到微信API参数，请先配置")
            print("💡 配置方法：")
            print("   1. 使用微信PC端访问目标公众号")
            print("   2. 使用抓包工具（如Charles、Fiddler）获取profile_ext请求")
            print("   3. 提取biz、uin、key参数并添加到配置文件")

            return {}

        except Exception as 错误:
            print(f"⚠️ 获取微信API参数失败: {错误}")
            return {}

    def _通过微信API获取文章(self, API参数: Dict[str, str], 公众号名称: str) -> List[Dict[str, str]]:
        """
        通过微信API获取公众号历史文章

        参数:
            API参数: 包含biz, uin, key的字典
            公众号名称: 公众号名称

        返回:
            文章列表
        """
        try:
            文章列表 = []
            offset = 0
            count = 10

            while len(文章列表) < 50:  # 最多获取50篇文章
                # 构建API URL
                url = f"https://mp.weixin.qq.com/mp/profile_ext?action=getmsg&__biz={API参数['biz']}&f=json&offset={offset}&count={count}&is_ok=1&scene=124&uin={API参数['uin']}&key={API参数['key']}"

                print(f"📡 请求微信API，offset={offset}")

                # 设置请求头
                headers = self.headers.copy()
                headers.update({
                    'Referer': f"https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz={API参数['biz']}&scene=124",
                    'X-Requested-With': 'XMLHttpRequest',
                })

                response = requests.get(url, headers=headers, timeout=30)

                if response.status_code != 200:
                    print(f"❌ API请求失败，状态码: {response.status_code}")
                    break

                try:
                    data = response.json()
                except:
                    print(f"❌ 响应不是有效的JSON格式")
                    break

                # 解析文章列表
                general_msg_list_str = data.get('general_msg_list', '')
                if not general_msg_list_str:
                    print("❌ 未找到文章列表数据")
                    break

                import json
                msg_list_json = json.loads(general_msg_list_str)
                art_list = msg_list_json.get('list', [])

                if not art_list:
                    print("📄 没有更多文章")
                    break

                # 解析每篇文章
                for art in art_list:
                    文章信息列表 = self._解析微信文章信息(art, 公众号名称)
                    文章列表.extend(文章信息列表)

                # 检查是否可以继续获取
                if not data.get('can_msg_continue', 0):
                    print("📄 已获取所有文章")
                    break

                offset += count

                # 添加延迟避免请求过快
                import time
                time.sleep(2)

            return 文章列表

        except Exception as 错误:
            print(f"❌ 微信API获取文章失败: {错误}")
            return []

    def _解析微信文章信息(self, art_json: dict, 公众号名称: str) -> List[Dict[str, str]]:
        """
        解析微信API返回的文章信息

        参数:
            art_json: 微信API返回的文章JSON数据
            公众号名称: 公众号名称

        返回:
            文章信息列表
        """
        文章列表 = []

        try:
            comm_msg_info = art_json.get('comm_msg_info', {})

            # 获取发布时间
            datetime_ms = comm_msg_info.get('datetime', 0)
            if datetime_ms:
                发布时间 = datetime.fromtimestamp(datetime_ms).strftime('%Y-%m-%d %H:%M')
            else:
                发布时间 = ""

            # 获取头条文章
            app_msg_ext_info = art_json.get('app_msg_ext_info')
            if app_msg_ext_info:
                标题 = app_msg_ext_info.get('title', '').strip()
                链接 = app_msg_ext_info.get('content_url', '').strip()
                摘要 = app_msg_ext_info.get('digest', '').strip()

                if 标题 and 链接:
                    文章列表.append({
                        '标题': 标题,
                        '链接': 链接,
                        '发布时间': 发布时间,
                        '摘要': 摘要,
                        '来源': '微信API',
                        '来源账号': 公众号名称
                    })

                # 获取次条文章
                multi_app_msg_item_list = app_msg_ext_info.get('multi_app_msg_item_list', [])
                for item in multi_app_msg_item_list:
                    标题 = item.get('title', '').strip()
                    链接 = item.get('content_url', '').strip()
                    摘要 = item.get('digest', '').strip()

                    if 标题 and 链接:
                        文章列表.append({
                            '标题': 标题,
                            '链接': 链接,
                            '发布时间': 发布时间,
                            '摘要': 摘要,
                            '来源': '微信API',
                            '来源账号': 公众号名称
                        })

        except Exception as 错误:
            print(f"⚠️ 解析文章信息失败: {错误}")

        return 文章列表

    def _通过搜狗微信搜索获取文章(self, 公众号名称: str, 公众号微信号: str = None) -> List[Dict[str, str]]:
        """
        通过搜狗微信搜索获取文章

        参数:
            公众号名称: 公众号名称
            公众号微信号: 公众号微信号（可选）

        返回:
            文章列表
        """
        try:
            import urllib.parse

            # 构建搜索查询
            if 公众号微信号:
                查询词 = f"{公众号名称} {公众号微信号}"
            else:
                查询词 = 公众号名称

            # 搜狗微信搜索URL
            搜索URL = f"https://weixin.sogou.com/weixin?type=1&query={urllib.parse.quote(查询词)}&ie=utf8"

            response = requests.get(搜索URL, headers=self.headers, timeout=30)
            response.encoding = 'utf-8'

            if response.status_code != 200:
                print(f"⚠️ 搜狗搜索请求失败: {response.status_code}")
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            文章列表 = []

            # 查找公众号链接
            results = soup.find_all('div', class_='results')

            for result in results:
                try:
                    # 查找公众号名称
                    title_elem = result.find('h3')
                    if not title_elem:
                        continue

                    result_title = title_elem.get_text().strip()
                    if 公众号名称 not in result_title:
                        continue

                    # 找到匹配的公众号，获取其链接
                    link_elem = result.find('a')
                    if not link_elem:
                        continue

                    公众号链接 = link_elem.get('href')
                    if not 公众号链接:
                        continue

                    print(f"✅ 找到公众号链接: {公众号链接}")

                    # 访问公众号页面获取最新文章
                    文章列表 = self._从搜狗公众号页面获取文章(公众号链接, 公众号名称)
                    break

                except Exception:
                    continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 搜狗微信搜索失败: {错误}")
            return []

    def _从搜狗公众号页面获取文章(self, 公众号链接: str, 公众号名称: str) -> List[Dict[str, str]]:
        """
        从搜狗公众号页面获取最新文章

        参数:
            公众号链接: 搜狗公众号页面链接
            公众号名称: 公众号名称

        返回:
            文章列表
        """
        try:
            response = requests.get(公众号链接, headers=self.headers, timeout=30)
            response.encoding = 'utf-8'

            if response.status_code != 200:
                print(f"⚠️ 访问公众号页面失败: {response.status_code}")
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            文章列表 = []

            # 查找文章列表
            articles = soup.find_all('div', class_='weui_media_box')

            for article in articles:
                try:
                    # 提取标题和链接
                    title_elem = article.find('h4', class_='weui_media_title')
                    if not title_elem:
                        continue

                    link_elem = title_elem.find('a')
                    if not link_elem:
                        continue

                    标题 = title_elem.get_text().strip()
                    链接 = link_elem.get('href')

                    # 提取发布时间
                    time_elem = article.find('p', class_='weui_media_extra_info')
                    发布时间 = time_elem.get_text().strip() if time_elem else ""

                    # 提取摘要
                    desc_elem = article.find('p', class_='weui_media_desc')
                    摘要 = desc_elem.get_text().strip() if desc_elem else ""

                    if 标题 and 链接:
                        文章列表.append({
                            '标题': 标题,
                            '链接': 链接,
                            '发布时间': 发布时间,
                            '摘要': 摘要,
                            '来源': '搜狗微信',
                            '来源账号': 公众号名称
                        })

                    if len(文章列表) >= 10:  # 限制数量
                        break

                except Exception:
                    continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 从公众号页面获取文章失败: {错误}")
            return []

    def _通过微信搜索获取文章(self, 公众号名称: str, 公众号微信号: str = None) -> List[Dict[str, str]]:
        """通过微信搜索获取最近1天的文章"""
        try:
            import urllib.parse
            import json

            # 微信搜索API（模拟微信搜索页面）
            if 公众号微信号:
                查询词 = f"{公众号名称} {公众号微信号}"
            else:
                查询词 = 公众号名称

            # 构建微信搜索URL（最近1天）
            搜索URL = f"https://weixin.sogou.com/weixin?type=2&query={urllib.parse.quote(查询词)}&ie=utf8&_sug_=n&_sug_type_=1&w=01019900&sut=0&sst0=1"

            response = requests.get(搜索URL, headers=self.headers, timeout=30)
            response.encoding = 'utf-8'

            if response.status_code != 200:
                print(f"⚠️ 微信搜索请求失败: {response.status_code}")
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            文章列表 = []

            # 解析搜索结果
            results = soup.find_all('div', class_='news-box')

            for result in results:
                try:
                    # 提取标题和链接
                    title_elem = result.find('h3')
                    if not title_elem:
                        continue

                    link_elem = title_elem.find('a')
                    if not link_elem:
                        continue

                    标题 = title_elem.get_text().strip()
                    链接 = link_elem.get('href')

                    # 提取发布时间
                    time_elem = result.find('span', class_='s2')
                    发布时间 = time_elem.get_text().strip() if time_elem else ""

                    # 提取摘要
                    desc_elem = result.find('p', class_='txt-info')
                    摘要 = desc_elem.get_text().strip() if desc_elem else ""

                    # 检查是否是微信文章
                    if 'mp.weixin.qq.com' in 链接:
                        文章列表.append({
                            '标题': 标题,
                            '链接': 链接,
                            '发布时间': 发布时间,
                            '摘要': 摘要,
                            '来源': '微信搜索',
                            '来源账号': 公众号名称
                        })

                        if len(文章列表) >= 10:  # 限制数量
                            break

                except Exception as e:
                    continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 微信搜索失败: {错误}")
            return []

    def _通过雪球获取文章(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过雪球平台获取相关文章"""
        try:
            # 首先尝试从配置文件获取雪球用户ID
            雪球用户ID = None
            for 账号ID, 配置信息 in self.公众号配置.items():
                if 配置信息.get('名称') == 公众号名称:
                    雪球用户ID = 配置信息.get('雪球用户ID')
                    break

            if 雪球用户ID:
                print(f"🎯 找到雪球用户ID: {雪球用户ID}")
                return self._从雪球用户页面获取文章(雪球用户ID, 公众号名称)
            else:
                print(f"🔍 未找到雪球用户ID，使用搜索方式")
                return self._通过雪球搜索获取文章(公众号名称)

        except Exception as 错误:
            print(f"⚠️ 雪球获取文章失败: {错误}")
            return []

    def _从雪球用户页面获取文章(self, 用户ID: str, 公众号名称: str) -> List[Dict[str, str]]:
        """从雪球用户页面直接获取最新文章"""
        try:
            # 雪球用户动态API
            动态URL = f"https://xueqiu.com/v4/statuses/user_timeline.json?user_id={用户ID}&page=1&count=20"

            # 雪球需要特殊的headers和cookie
            雪球headers = self.headers.copy()
            雪球headers.update({
                'Referer': f'https://xueqiu.com/u/{用户ID}',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/plain, */*',
            })

            # 先访问用户主页获取cookie
            主页URL = f"https://xueqiu.com/u/{用户ID}"
            session = requests.Session()
            session.get(主页URL, headers=self.headers, timeout=30)

            # 获取用户动态
            response = session.get(动态URL, headers=雪球headers, timeout=30)

            if response.status_code != 200:
                print(f"⚠️ 雪球用户动态请求失败: {response.status_code}")
                return []

            data = response.json()
            文章列表 = []

            if 'statuses' in data:
                print(f"📊 获取到 {len(data['statuses'])} 条雪球动态")

                for item in data['statuses']:
                    try:
                        # 提取文章信息
                        text = item.get('text', '').strip()
                        if not text or len(text) < 10:  # 过滤太短的内容
                            continue

                        # 生成标题（取前50个字符）
                        标题 = text[:50].replace('\n', ' ').strip()
                        if len(text) > 50:
                            标题 += '...'

                        # 构建雪球文章链接
                        status_id = item.get('id')
                        链接 = f"https://xueqiu.com/{用户ID}/{status_id}"

                        # 转换时间戳
                        created_at = item.get('created_at', 0)
                        if created_at:
                            发布时间 = datetime.fromtimestamp(created_at / 1000).strftime('%Y-%m-%d %H:%M')
                        else:
                            发布时间 = ""

                        # 提取摘要
                        摘要 = text[:200] + '...' if len(text) > 200 else text

                        # 检查是否包含投资相关关键词
                        投资关键词 = ['套利', '转债', '投资', '股票', '基金', '债券', '收益', '风险', '市场']
                        if any(keyword in text for keyword in 投资关键词):
                            文章列表.append({
                                '标题': 标题,
                                '链接': 链接,
                                '发布时间': 发布时间,
                                '摘要': 摘要,
                                '来源': '雪球',
                                '来源账号': item.get('user', {}).get('screen_name', 公众号名称)
                            })

                        if len(文章列表) >= 10:
                            break

                    except Exception:
                        continue

            print(f"✅ 从雪球用户页面获取到 {len(文章列表)} 篇相关文章")
            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 雪球用户页面获取失败: {错误}")
            return []

    def _通过雪球搜索获取文章(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过雪球搜索获取相关文章（备用方法）"""
        try:
            import urllib.parse

            # 雪球搜索API
            查询词 = 公众号名称
            搜索URL = f"https://xueqiu.com/query/v1/search/status?count=20&comment=0&symbol=&hl=0&source=all&sort=time&page=1&q={urllib.parse.quote(查询词)}"

            # 雪球需要特殊的headers
            雪球headers = self.headers.copy()
            雪球headers.update({
                'Referer': 'https://xueqiu.com/',
                'X-Requested-With': 'XMLHttpRequest'
            })

            response = requests.get(搜索URL, headers=雪球headers, timeout=30)

            if response.status_code != 200:
                print(f"⚠️ 雪球搜索请求失败: {response.status_code}")
                return []

            data = response.json()
            文章列表 = []

            if 'list' in data:
                for item in data['list']:
                    try:
                        标题 = item.get('title', '').strip()
                        if not 标题:
                            标题 = item.get('text', '')[:50] + '...'

                        # 构建雪球文章链接
                        status_id = item.get('id')
                        链接 = f"https://xueqiu.com/{item.get('user', {}).get('id', '')}/{status_id}"

                        # 转换时间戳
                        created_at = item.get('created_at', 0)
                        if created_at:
                            发布时间 = datetime.fromtimestamp(created_at / 1000).strftime('%Y-%m-%d %H:%M')
                        else:
                            发布时间 = ""

                        摘要 = item.get('text', '')[:100] + '...' if item.get('text') else ""

                        文章列表.append({
                            '标题': 标题,
                            '链接': 链接,
                            '发布时间': 发布时间,
                            '摘要': 摘要,
                            '来源': '雪球搜索',
                            '来源账号': item.get('user', {}).get('screen_name', 公众号名称)
                        })

                        if len(文章列表) >= 10:
                            break

                    except Exception:
                        continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 雪球搜索失败: {错误}")
            return []

    def _通过集思录获取文章(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过集思录获取相关文章"""
        try:
            import urllib.parse

            # 集思录搜索
            查询词 = 公众号名称
            搜索URL = f"https://www.jisilu.cn/search/?q={urllib.parse.quote(查询词)}"

            response = requests.get(搜索URL, headers=self.headers, timeout=30)
            response.encoding = 'utf-8'

            if response.status_code != 200:
                print(f"⚠️ 集思录搜索请求失败: {response.status_code}")
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            文章列表 = []

            # 解析搜索结果
            results = soup.find_all('div', class_='aw-item')

            for result in results:
                try:
                    # 提取标题和链接
                    title_elem = result.find('h4')
                    if not title_elem:
                        continue

                    link_elem = title_elem.find('a')
                    if not link_elem:
                        continue

                    标题 = title_elem.get_text().strip()
                    链接 = "https://www.jisilu.cn" + link_elem.get('href')

                    # 提取发布时间
                    time_elem = result.find('span', class_='text-color-999')
                    发布时间 = time_elem.get_text().strip() if time_elem else ""

                    # 提取摘要
                    desc_elem = result.find('p', class_='aw-text-color-999')
                    摘要 = desc_elem.get_text().strip() if desc_elem else ""

                    文章列表.append({
                        '标题': 标题,
                        '链接': 链接,
                        '发布时间': 发布时间,
                        '摘要': 摘要,
                        '来源': '集思录',
                        '来源账号': 公众号名称
                    })

                    if len(文章列表) >= 10:
                        break

                except Exception:
                    continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 集思录搜索失败: {错误}")
            return []

    def _通过知乎获取文章(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过知乎获取相关文章"""
        try:
            import urllib.parse

            # 知乎搜索API
            查询词 = 公众号名称
            搜索URL = f"https://www.zhihu.com/api/v4/search_v3?t=general&q={urllib.parse.quote(查询词)}&correction=1&offset=0&limit=20&lc_idx=0&show_all_topics=0&search_source=Filter"

            知乎headers = self.headers.copy()
            知乎headers.update({
                'Referer': 'https://www.zhihu.com/',
                'X-Requested-With': 'XMLHttpRequest'
            })

            response = requests.get(搜索URL, headers=知乎headers, timeout=30)

            if response.status_code != 200:
                print(f"⚠️ 知乎搜索请求失败: {response.status_code}")
                return []

            data = response.json()
            文章列表 = []

            if 'data' in data:
                for item in data['data']:
                    try:
                        if item.get('type') != 'search_result':
                            continue

                        object_data = item.get('object', {})
                        标题 = object_data.get('title', '').strip()

                        if not 标题:
                            continue

                        # 构建知乎文章链接
                        if object_data.get('type') == 'answer':
                            链接 = f"https://www.zhihu.com/question/{object_data.get('question', {}).get('id')}/answer/{object_data.get('id')}"
                        elif object_data.get('type') == 'article':
                            链接 = f"https://zhuanlan.zhihu.com/p/{object_data.get('id')}"
                        else:
                            continue

                        # 提取发布时间
                        created_time = object_data.get('created_time', 0)
                        if created_time:
                            发布时间 = datetime.fromtimestamp(created_time).strftime('%Y-%m-%d %H:%M')
                        else:
                            发布时间 = ""

                        摘要 = object_data.get('excerpt', '')[:100] + '...' if object_data.get('excerpt') else ""

                        文章列表.append({
                            '标题': 标题,
                            '链接': 链接,
                            '发布时间': 发布时间,
                            '摘要': 摘要,
                            '来源': '知乎',
                            '来源账号': object_data.get('author', {}).get('name', 公众号名称)
                        })

                        if len(文章列表) >= 10:
                            break

                    except Exception:
                        continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 知乎搜索失败: {错误}")
            return []

    def _通过今日头条获取文章(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过今日头条获取相关文章"""
        try:
            import urllib.parse

            # 今日头条搜索
            查询词 = 公众号名称
            搜索URL = f"https://www.toutiao.com/api/search/content/?keyword={urllib.parse.quote(查询词)}&autoload=true&count=20&cur_tab=1&from=search_tab"

            头条headers = self.headers.copy()
            头条headers.update({
                'Referer': 'https://www.toutiao.com/',
            })

            response = requests.get(搜索URL, headers=头条headers, timeout=30)

            if response.status_code != 200:
                print(f"⚠️ 今日头条搜索请求失败: {response.status_code}")
                return []

            data = response.json()
            文章列表 = []

            if 'data' in data:
                for item in data['data']:
                    try:
                        标题 = item.get('title', '').strip()
                        if not 标题:
                            continue

                        # 构建头条文章链接
                        article_url = item.get('article_url', '')
                        if not article_url:
                            continue

                        链接 = f"https://www.toutiao.com{article_url}"

                        # 提取发布时间
                        publish_time = item.get('publish_time', 0)
                        if publish_time:
                            发布时间 = datetime.fromtimestamp(publish_time).strftime('%Y-%m-%d %H:%M')
                        else:
                            发布时间 = ""

                        摘要 = item.get('abstract', '')[:100] + '...' if item.get('abstract') else ""

                        文章列表.append({
                            '标题': 标题,
                            '链接': 链接,
                            '发布时间': 发布时间,
                            '摘要': 摘要,
                            '来源': '今日头条',
                            '来源账号': item.get('source', 公众号名称)
                        })

                        if len(文章列表) >= 10:
                            break

                    except Exception:
                        continue

            return 文章列表

        except Exception as 错误:
            print(f"⚠️ 今日头条搜索失败: {错误}")
            return []

    def _去重并排序文章(self, 文章列表: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """去重并按时间排序文章"""
        try:
            # 去重（基于链接）
            seen_links = set()
            unique_articles = []

            for article in 文章列表:
                link = article.get('链接', '')
                if link and link not in seen_links:
                    seen_links.add(link)
                    unique_articles.append(article)

            # 按发布时间排序（最新的在前）
            def get_sort_key(article):
                发布时间 = article.get('发布时间', '')
                if 发布时间:
                    try:
                        # 尝试解析时间
                        if '年' in 发布时间:
                            return datetime.strptime(发布时间, '%Y年%m月%d日')
                        elif '-' in 发布时间 and ':' in 发布时间:
                            return datetime.strptime(发布时间, '%Y-%m-%d %H:%M')
                        elif '-' in 发布时间:
                            return datetime.strptime(发布时间, '%Y-%m-%d')
                        else:
                            return datetime.min
                    except:
                        return datetime.min
                return datetime.min

            unique_articles.sort(key=get_sort_key, reverse=True)
            return unique_articles

        except Exception as 错误:
            print(f"⚠️ 文章去重排序失败: {错误}")
            return 文章列表

    def _通过搜索获取文章列表(self, 公众号名称: str, 公众号微信号: str = None) -> List[Dict[str, str]]:
        """通过搜索引擎获取文章列表"""
        try:
            import urllib.parse

            # 构建搜索查询
            if 公众号微信号:
                查询词 = f"site:mp.weixin.qq.com {公众号名称} {公众号微信号}"
            else:
                查询词 = f"site:mp.weixin.qq.com {公众号名称}"

            # 使用多个搜索引擎
            搜索引擎列表 = [
                f"https://www.google.com/search?q={urllib.parse.quote(查询词)}&tbs=qdr:d2",  # 最近2天
                f"https://www.bing.com/search?q={urllib.parse.quote(查询词)}&filters=ex1:\"ez1_1\"",  # 最近24小时
            ]

            文章列表 = []

            for 搜索链接 in 搜索引擎列表:
                try:
                    response = requests.get(搜索链接, headers=self.headers, timeout=30)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # 提取搜索结果中的微信文章链接
                        links = soup.find_all('a', href=True)
                        for link in links:
                            href = link.get('href')
                            if href and 'mp.weixin.qq.com/s/' in href:
                                # 清理链接
                                if href.startswith('/url?q='):
                                    href = urllib.parse.unquote(href.split('&')[0][7:])

                                title = link.get_text().strip()
                                if title and len(title) > 5:  # 过滤掉太短的标题
                                    文章列表.append({
                                        '标题': title,
                                        '链接': href,
                                        '发布时间': '',  # 需要进一步获取
                                        '摘要': '',
                                        '来源账号': 公众号名称
                                    })

                        if len(文章列表) >= 10:  # 限制数量
                            break

                except Exception as e:
                    print(f"⚠️ 搜索引擎 {搜索链接} 失败: {e}")
                    continue

            # 去重
            seen_links = set()
            unique_articles = []
            for article in 文章列表:
                if article['链接'] not in seen_links:
                    seen_links.add(article['链接'])
                    unique_articles.append(article)

            return unique_articles[:10]  # 返回前10篇

        except Exception as 错误:
            print(f"⚠️ 搜索获取文章失败: {错误}")
            return []

    def _通过RSS获取文章列表(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过RSS源获取文章列表"""
        # 这里可以实现RSS解析逻辑
        # 目前大多数微信公众号没有公开RSS，所以返回空列表
        return []

    def _通过第三方API获取文章列表(self, 公众号名称: str) -> List[Dict[str, str]]:
        """通过第三方API获取文章列表"""
        # 这里可以集成第三方API，如微信公众号API、爬虫服务等
        # 目前返回空列表
        return []

    def _检查文章时间(self, 发布时间_文本: str) -> bool:
        """
        检查文章发布时间是否在采集范围内

        参数:
            发布时间_文本: 发布时间文本

        返回:
            是否在时间范围内
        """
        try:
            # 尝试解析不同的时间格式
            时间格式列表 = [
                '%Y-%m-%d',
                '%Y年%m月%d日',
                '%m月%d日',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d %H:%M:%S'
            ]

            文章时间 = None
            for 格式 in 时间格式列表:
                try:
                    if '年' in 发布时间_文本 and '月' in 发布时间_文本 and '日' in 发布时间_文本:
                        文章时间 = datetime.strptime(发布时间_文本, '%Y年%m月%d日')
                    elif '月' in 发布时间_文本 and '日' in 发布时间_文本:
                        # 如果只有月日，假设是今年
                        当前年份 = datetime.now().year
                        月日 = 发布时间_文本.replace('月', '-').replace('日', '')
                        文章时间 = datetime.strptime(f"{当前年份}-{月日}", '%Y-%m-%d')
                    else:
                        文章时间 = datetime.strptime(发布时间_文本, 格式)
                    break
                except ValueError:
                    continue

            if 文章时间 is None:
                if not 发布时间_文本.strip():
                    print(f"⚠️ 无法获取文章发布时间，拒绝采集")
                    return False
                else:
                    print(f"⚠️ 无法解析时间格式: '{发布时间_文本}'，拒绝采集")
                    return False

            # 检查是否在时间范围内
            时间差 = datetime.now() - 文章时间
            print(f"📅 文章发布于 {文章时间.strftime('%Y-%m-%d %H:%M:%S')}，距今 {时间差.days} 天")

            是否在范围内 = 文章时间 >= self.截止时间
            if 是否在范围内:
                print(f"✅ 文章在时间范围内")
            else:
                print(f"❌ 文章超出时间范围（超过{self.采集天数}天）")

            return 是否在范围内

        except Exception as 错误:
            print(f"⚠️ 时间检查出错: {错误}，拒绝采集")
            return False

    def 采集文章(self, 文章链接: str, 来源账号: str = "未知") -> Optional[Dict[str, Any]]:
        """
        采集微信文章内容
        
        参数:
            文章链接: 微信文章链接
            来源账号: 来源公众号名称
            
        返回:
            文章数据字典
        """
        print(f"🔍 正在采集文章: {文章链接}")
        
        try:
            response = requests.get(文章链接, headers=self.headers, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取标题
                title_elem = soup.find('h1', class_='rich_media_title')
                标题 = title_elem.get_text().strip() if title_elem else "未知标题"
                
                # 提取作者
                author_elem = soup.find('a', class_='rich_media_meta_link')
                if not author_elem:
                    author_elem = soup.find('span', class_='rich_media_meta_text')
                作者 = author_elem.get_text().strip() if author_elem else 来源账号
                
                # 提取发布时间 - 尝试多种方式
                发布时间_文本 = self._提取发布时间(soup)
                发布时间 = 发布时间_文本

                print(f"🕒 提取到的发布时间: '{发布时间_文本}'")

                # 检查文章是否在时间范围内
                if not self._检查文章时间(发布时间_文本):
                    print(f"⏰ 文章发布时间超出范围: {发布时间_文本}")
                    return None
                
                # 提取正文内容
                content_elem = soup.find('div', class_='rich_media_content')
                if content_elem:
                    # 移除脚本和样式
                    for script in content_elem(["script", "style"]):
                        script.decompose()
                    
                    # 获取文本内容
                    内容 = content_elem.get_text(separator='\n', strip=True)
                    # 清理多余的空行
                    内容 = re.sub(r'\n\s*\n', '\n\n', 内容)
                    内容 = re.sub(r'\n{3,}', '\n\n', 内容)
                    
                    # 提取图片
                    图片列表 = []
                    for img in content_elem.find_all('img'):
                        img_src = img.get('data-src') or img.get('src')
                        if img_src and 'http' in img_src:
                            图片列表.append(img_src)
                else:
                    内容 = "无法提取正文内容"
                    图片列表 = []
                
                文章数据 = {
                    '标题': 标题,
                    '内容': 内容,
                    '作者': 作者,
                    '来源账号': 来源账号,
                    '链接': 文章链接,
                    '发布时间': 发布时间,
                    '采集时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '图片列表': 图片列表,
                    '字数': len(内容),
                    '图片数量': len(图片列表)
                }
                
                print(f"✅ 文章采集成功")
                print(f"📝 标题: {标题}")
                print(f"👤 作者: {作者}")
                print(f"📊 字数: {len(内容)}")
                print(f"🖼️ 图片数量: {len(图片列表)}")
                
                return 文章数据
                
            else:
                print(f"❌ 采集失败，状态码: {response.status_code}")
                return None
                
        except Exception as 错误:
            print(f"❌ 采集文章失败: {错误}")
            return None
    
    def 保存文章到文件(self, 文章数据: Dict[str, Any]) -> Optional[str]:
        """
        保存文章到文件系统
        
        参数:
            文章数据: 文章数据字典
            
        返回:
            保存的文件路径，如果保存失败返回None
        """
        try:
            # 创建文件名（使用时间戳和标题）
            时间戳 = datetime.now().strftime("%Y%m%d_%H%M%S")
            安全标题 = re.sub(r'[<>:"/\\|?*]', '_', 文章数据['标题'])[:50]  # 限制长度并替换非法字符
            来源账号 = re.sub(r'[<>:"/\\|?*]', '_', 文章数据.get('来源账号', '未知'))
            文件名 = f"{时间戳}_{来源账号}_{安全标题}.md"
            文件路径 = os.path.join(self.保存目录, 文件名)
            
            # 准备文章内容
            文章内容 = f"""# {文章数据['标题']}

**作者**: {文章数据.get('作者', '未知')}
**来源**: {文章数据.get('来源账号', '未知')}
**发布时间**: {文章数据.get('发布时间', '未知')}
**采集时间**: {文章数据.get('采集时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}
**原文链接**: {文章数据.get('链接', '')}
**字数**: {文章数据.get('字数', 0)}
**图片数量**: {文章数据.get('图片数量', 0)}

---

## 文章内容

{文章数据.get('内容', '')}

"""

            # 如果有图片，添加图片信息
            图片列表 = 文章数据.get('图片列表', [])
            if 图片列表:
                文章内容 += "\n---\n\n## 文章图片\n\n"
                for i, 图片链接 in enumerate(图片列表, 1):
                    文章内容 += f"{i}. ![图片{i}]({图片链接})\n"
                    文章内容 += f"   链接: {图片链接}\n\n"
            
            文章内容 += f"\n---\n\n**采集工具**: 简化文章采集器\n**采集时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            # 保存到文件
            with open(文件路径, 'w', encoding='utf-8') as f:
                f.write(文章内容)
            
            print(f"💾 文章已保存到文件: {文件路径}")
            return 文件路径
            
        except Exception as 错误:
            print(f"❌ 保存文章到文件失败: {错误}")
            return None
    
    def 批量采集文章(self, 文章链接列表: List[Dict[str, str]]) -> List[str]:
        """
        批量采集文章
        
        参数:
            文章链接列表: [{'链接': 'url', '来源账号': 'name'}, ...]
            
        返回:
            保存的文件路径列表
        """
        保存文件列表 = []
        
        print(f"🚀 开始批量采集 {len(文章链接列表)} 篇文章")
        
        for i, 文章信息 in enumerate(文章链接列表, 1):
            print(f"\n📖 采集第 {i}/{len(文章链接列表)} 篇文章")
            
            文章数据 = self.采集文章(
                文章信息['链接'], 
                文章信息.get('来源账号', '未知')
            )
            
            if 文章数据:
                文件路径 = self.保存文章到文件(文章数据)
                if 文件路径:
                    保存文件列表.append(文件路径)
            
            # 添加延迟避免请求过快
            if i < len(文章链接列表):
                print("⏳ 等待 2 秒...")
                import time
                time.sleep(2)
        
        print(f"\n🎉 批量采集完成！成功保存 {len(保存文件列表)} 篇文章")
        return 保存文件列表

    def 从配置文件采集(self) -> List[str]:
        """
        从配置文件中获取公众号信息并全自动批量采集最近2天的文章

        返回:
            保存的文件路径列表
        """
        print("📋 开始全自动采集最近2天的文章...")

        if not self.公众号配置:
            print("❌ 没有找到启用的公众号配置")
            return []

        所有文章列表 = []

        # 遍历每个启用的公众号
        for 账号ID, 配置信息 in self.公众号配置.items():
            if not 配置信息.get('是否启用', False):
                continue

            公众号名称 = 配置信息['名称']
            公众号微信号 = 配置信息.get('微信号', '')

            print(f"\n🤖 正在全自动处理公众号: {公众号名称}")
            if 公众号微信号:
                print(f"   微信号: {公众号微信号}")

            # 自动获取公众号文章列表
            文章列表 = self.获取公众号文章列表(公众号名称, 公众号微信号)

            if not 文章列表:
                print(f"⚠️ 无法自动获取公众号 '{公众号名称}' 的文章，尝试使用示例链接...")

                # 如果自动获取失败，尝试使用示例链接
                示例链接 = 配置信息.get('示例链接', [])
                if 示例链接:
                    for 链接 in 示例链接:
                        print(f"🔍 检查示例链接: {链接}")
                        文章数据 = self.采集文章(链接, 公众号名称)
                        if 文章数据:
                            文章列表.append({
                                '标题': 文章数据.get('标题', '未知标题'),
                                '链接': 链接,
                                '发布时间': 文章数据.get('发布时间', ''),
                                '摘要': '',
                                '来源账号': 公众号名称
                            })
                continue

            print(f"✅ 获取到 {len(文章列表)} 篇文章，正在筛选最近2天的文章...")

            # 为每篇文章获取详细的发布时间（如果还没有的话）
            符合条件的文章 = []
            for 文章 in 文章列表:
                发布时间 = 文章.get('发布时间', '')

                # 如果没有发布时间，尝试从文章页面获取
                if not 发布时间:
                    print(f"🔍 获取文章发布时间: {文章['标题'][:30]}...")
                    文章数据 = self.采集文章(文章['链接'], 公众号名称)
                    if 文章数据:
                        发布时间 = 文章数据.get('发布时间', '')
                        文章['发布时间'] = 发布时间

                # 检查是否在时间范围内
                if self._检查文章时间(发布时间):
                    符合条件的文章.append({
                        '链接': 文章['链接'],
                        '来源账号': 公众号名称,
                        '账号ID': 账号ID,
                        '标题': 文章['标题'],
                        '发布时间': 发布时间
                    })
                    print(f"✅ 符合条件: {文章['标题'][:30]}...")
                else:
                    print(f"⏰ 超出时间范围: {文章['标题'][:30]}...")

            print(f"📊 {公众号名称}: 总共 {len(文章列表)} 篇文章，符合条件 {len(符合条件的文章)} 篇")
            所有文章列表.extend(符合条件的文章)

        if not 所有文章列表:
            print("\n❌ 没有找到符合时间条件的文章")
            print("💡 可能的原因：")
            print("   1. 公众号最近2天没有发布文章")
            print("   2. 搜索引擎暂时无法访问")
            print("   3. 公众号名称或微信号配置不正确")
            return []

        print(f"\n🎉 总计找到 {len(所有文章列表)} 篇最近2天的文章:")
        for i, 文章信息 in enumerate(所有文章列表, 1):
            print(f"  {i}. {文章信息['来源账号']}: {文章信息['标题'][:40]}... ({文章信息['发布时间']})")

        # 批量采集
        需要采集的文章 = []
        for 文章 in 所有文章列表:
            需要采集的文章.append({
                '链接': 文章['链接'],
                '来源账号': 文章['来源账号']
            })

        print(f"\n🚀 开始批量采集 {len(需要采集的文章)} 篇文章...")
        return self.批量采集文章(需要采集的文章)

    def 获取公众号配置信息(self) -> Dict[str, Any]:
        """
        获取当前的公众号配置信息

        返回:
            公众号配置信息
        """
        return {
            '公众号配置': self.公众号配置,
            '采集器配置': self.采集器配置,
            '文件保存配置': self.文件保存配置,
            '采集天数': self.采集天数,
            '截止时间': self.截止时间.strftime('%Y-%m-%d %H:%M:%S')
        }


def 主函数():
    """主函数 - 演示使用"""
    print("🚀 简化文章采集器 - 最近2天文章采集")
    print("=" * 60)

    # 创建采集器，采集最近2天的文章
    采集器 = 简化文章采集器(采集天数=2)

    # 显示配置信息
    配置信息 = 采集器.获取公众号配置信息()
    print(f"\n📊 配置信息:")
    print(f"   采集天数: {配置信息['采集天数']} 天")
    print(f"   截止时间: {配置信息['截止时间']}")
    print(f"   启用的公众号数量: {len(配置信息['公众号配置'])}")

    for 账号ID, 信息 in 配置信息['公众号配置'].items():
        print(f"   - {信息['名称']} ({信息['采集频率']})")

    print(f"\n🔄 开始从配置文件采集文章...")

    # 从配置文件批量采集
    保存文件列表 = 采集器.从配置文件采集()

    if 保存文件列表:
        print(f"\n✅ 采集完成！成功保存 {len(保存文件列表)} 篇文章:")
        for i, 文件路径 in enumerate(保存文件列表, 1):
            文件名 = os.path.basename(文件路径)
            print(f"   {i}. {文件名}")
    else:
        print("\n❌ 没有采集到符合条件的文章")
        print("   可能原因:")
        print("   1. 配置文件中的示例链接为空")
        print("   2. 文章发布时间超出了2天范围")
        print("   3. 网络连接问题")


if __name__ == "__main__":
    主函数()
