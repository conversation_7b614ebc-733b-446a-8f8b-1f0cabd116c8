#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件备份和恢复工具
用于备份和恢复套利信息提取配置文件

作者: AI助手
日期: 2025-07-29
"""

import os
import shutil
from datetime import datetime
import yaml

class 配置备份工具:
    """配置文件备份和恢复工具"""
    
    def __init__(self):
        """初始化备份工具"""
        self.配置文件路径 = os.path.join(os.path.dirname(__file__), '套利信息提取规则.yaml')
        self.备份目录 = os.path.join(os.path.dirname(__file__), '配置备份')
        
        # 确保备份目录存在
        if not os.path.exists(self.备份目录):
            os.makedirs(self.备份目录)
            print(f"✅ 创建备份目录: {self.备份目录}")
    
    def 创建备份(self, 备份名称: str = None) -> str:
        """创建配置文件备份"""
        try:
            if not os.path.exists(self.配置文件路径):
                print(f"❌ 配置文件不存在: {self.配置文件路径}")
                return None
            
            # 生成备份文件名
            if 备份名称 is None:
                时间戳 = datetime.now().strftime("%Y%m%d_%H%M%S")
                备份名称 = f"套利信息提取规则_备份_{时间戳}.yaml"
            elif not 备份名称.endswith('.yaml'):
                备份名称 += '.yaml'
            
            备份文件路径 = os.path.join(self.备份目录, 备份名称)
            
            # 复制文件
            shutil.copy2(self.配置文件路径, 备份文件路径)
            
            print(f"✅ 配置文件备份成功: {备份文件路径}")
            return 备份文件路径
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    
    def 列出备份文件(self) -> list:
        """列出所有备份文件"""
        try:
            备份文件列表 = []
            
            if not os.path.exists(self.备份目录):
                print("📁 备份目录不存在")
                return 备份文件列表
            
            for 文件名 in os.listdir(self.备份目录):
                if 文件名.endswith('.yaml'):
                    文件路径 = os.path.join(self.备份目录, 文件名)
                    文件信息 = os.stat(文件路径)
                    修改时间 = datetime.fromtimestamp(文件信息.st_mtime)
                    
                    备份文件列表.append({
                        '文件名': 文件名,
                        '文件路径': 文件路径,
                        '修改时间': 修改时间,
                        '文件大小': 文件信息.st_size
                    })
            
            # 按修改时间排序
            备份文件列表.sort(key=lambda x: x['修改时间'], reverse=True)
            
            return 备份文件列表
            
        except Exception as e:
            print(f"❌ 列出备份文件失败: {e}")
            return []
    
    def 恢复备份(self, 备份文件名: str) -> bool:
        """从备份恢复配置文件"""
        try:
            备份文件路径 = os.path.join(self.备份目录, 备份文件名)
            
            if not os.path.exists(备份文件路径):
                print(f"❌ 备份文件不存在: {备份文件路径}")
                return False
            
            # 验证备份文件是否有效
            if not self.验证配置文件(备份文件路径):
                print(f"❌ 备份文件格式无效: {备份文件路径}")
                return False
            
            # 先备份当前配置
            当前备份 = self.创建备份("恢复前备份")
            if 当前备份:
                print(f"📋 当前配置已备份为: {os.path.basename(当前备份)}")
            
            # 恢复配置文件
            shutil.copy2(备份文件路径, self.配置文件路径)
            
            print(f"✅ 配置文件恢复成功: {备份文件名}")
            return True
            
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
            return False
    
    def 验证配置文件(self, 文件路径: str) -> bool:
        """验证配置文件格式是否正确"""
        try:
            with open(文件路径, 'r', encoding='utf-8') as f:
                配置数据 = yaml.safe_load(f)
            
            # 检查必需的配置项
            必需配置 = [
                '基本设置', '文章生成限制', '套利信息提取', 
                '报告结构', '质量控制', '风险提示', 
                '禁止内容', '合规要求', '输出格式'
            ]
            
            for 配置项 in 必需配置:
                if 配置项 not in 配置数据:
                    print(f"❌ 缺少必需配置项: {配置项}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 配置文件验证失败: {e}")
            return False
    
    def 删除备份(self, 备份文件名: str) -> bool:
        """删除指定的备份文件"""
        try:
            备份文件路径 = os.path.join(self.备份目录, 备份文件名)
            
            if not os.path.exists(备份文件路径):
                print(f"❌ 备份文件不存在: {备份文件路径}")
                return False
            
            os.remove(备份文件路径)
            print(f"✅ 备份文件删除成功: {备份文件名}")
            return True
            
        except Exception as e:
            print(f"❌ 删除备份失败: {e}")
            return False
    
    def 清理旧备份(self, 保留数量: int = 10) -> int:
        """清理旧的备份文件，只保留最新的几个"""
        try:
            备份文件列表 = self.列出备份文件()
            
            if len(备份文件列表) <= 保留数量:
                print(f"📋 当前备份文件数量({len(备份文件列表)})未超过保留数量({保留数量})")
                return 0
            
            删除数量 = 0
            需要删除的文件 = 备份文件列表[保留数量:]
            
            for 文件信息 in 需要删除的文件:
                if self.删除备份(文件信息['文件名']):
                    删除数量 += 1
            
            print(f"✅ 清理完成，删除了 {删除数量} 个旧备份文件")
            return 删除数量
            
        except Exception as e:
            print(f"❌ 清理旧备份失败: {e}")
            return 0

def 显示菜单():
    """显示操作菜单"""
    print("\n" + "=" * 50)
    print("📋 配置文件备份和恢复工具")
    print("=" * 50)
    print("1. 创建备份")
    print("2. 列出备份文件")
    print("3. 恢复备份")
    print("4. 删除备份")
    print("5. 清理旧备份")
    print("6. 验证当前配置")
    print("0. 退出")
    print("=" * 50)

def 主函数():
    """主函数"""
    备份工具 = 配置备份工具()
    
    while True:
        显示菜单()
        
        try:
            选择 = input("请选择操作 (0-6): ").strip()
            
            if 选择 == '0':
                print("👋 再见！")
                break
                
            elif 选择 == '1':
                备份名称 = input("请输入备份名称 (留空使用默认名称): ").strip()
                if not 备份名称:
                    备份名称 = None
                备份工具.创建备份(备份名称)
                
            elif 选择 == '2':
                备份文件列表 = 备份工具.列出备份文件()
                if 备份文件列表:
                    print(f"\n📁 找到 {len(备份文件列表)} 个备份文件:")
                    print("-" * 80)
                    print(f"{'序号':<4} {'文件名':<40} {'修改时间':<20} {'大小':<10}")
                    print("-" * 80)
                    for i, 文件信息 in enumerate(备份文件列表, 1):
                        文件名 = 文件信息['文件名']
                        修改时间 = 文件信息['修改时间'].strftime('%Y-%m-%d %H:%M:%S')
                        文件大小 = f"{文件信息['文件大小']} B"
                        print(f"{i:<4} {文件名:<40} {修改时间:<20} {文件大小:<10}")
                else:
                    print("📁 没有找到备份文件")
                    
            elif 选择 == '3':
                备份文件列表 = 备份工具.列出备份文件()
                if not 备份文件列表:
                    print("❌ 没有可用的备份文件")
                    continue
                
                print(f"\n📁 可用的备份文件:")
                for i, 文件信息 in enumerate(备份文件列表, 1):
                    print(f"{i}. {文件信息['文件名']} ({文件信息['修改时间'].strftime('%Y-%m-%d %H:%M:%S')})")
                
                try:
                    序号 = int(input("请选择要恢复的备份文件序号: ").strip())
                    if 1 <= 序号 <= len(备份文件列表):
                        选中文件 = 备份文件列表[序号 - 1]
                        确认 = input(f"确认恢复备份 '{选中文件['文件名']}'? (y/N): ").strip().lower()
                        if 确认 == 'y':
                            备份工具.恢复备份(选中文件['文件名'])
                        else:
                            print("❌ 操作已取消")
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif 选择 == '4':
                备份文件列表 = 备份工具.列出备份文件()
                if not 备份文件列表:
                    print("❌ 没有可删除的备份文件")
                    continue
                
                print(f"\n📁 可删除的备份文件:")
                for i, 文件信息 in enumerate(备份文件列表, 1):
                    print(f"{i}. {文件信息['文件名']}")
                
                try:
                    序号 = int(input("请选择要删除的备份文件序号: ").strip())
                    if 1 <= 序号 <= len(备份文件列表):
                        选中文件 = 备份文件列表[序号 - 1]
                        确认 = input(f"确认删除备份 '{选中文件['文件名']}'? (y/N): ").strip().lower()
                        if 确认 == 'y':
                            备份工具.删除备份(选中文件['文件名'])
                        else:
                            print("❌ 操作已取消")
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif 选择 == '5':
                try:
                    保留数量 = int(input("请输入要保留的备份文件数量 (默认10): ").strip() or "10")
                    备份工具.清理旧备份(保留数量)
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif 选择 == '6':
                if 备份工具.验证配置文件(备份工具.配置文件路径):
                    print("✅ 当前配置文件格式正确")
                else:
                    print("❌ 当前配置文件格式有误")
                    
            else:
                print("❌ 无效的选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n❌ 操作被中断")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
