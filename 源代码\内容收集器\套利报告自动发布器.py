# -*- coding: utf-8 -*-
"""
套利报告自动发布器

作者: AI助手
日期: 2025-07-29
功能: 将套利分析报告自动发布到微信公众号草稿
"""

import os
import sys
import glob
from datetime import datetime, date
import re

# 添加自动发布器路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(os.path.dirname(当前目录))
自动发布器路径 = os.path.join(根目录, "源代码", "自动发布器")
sys.path.append(自动发布器路径)

try:
    from 微信自动发布器 import 微信自动发布器
    from 文章处理器 import 文章处理器
    from AI配图系统 import AI配图系统
    print("✅ 成功导入微信发布系统模块")
except ImportError as e:
    print(f"❌ 导入微信发布系统失败: {e}")
    print("💡 请确保微信公众号自动发布系统已正确安装")


class 套利报告发布器:
    """套利分析报告自动发布器"""
    
    def __init__(self):
        """初始化发布器"""
        self.根目录 = 根目录
        self.采集结果目录 = os.path.join(self.根目录, "采集结果")
        
        # 初始化微信发布器
        try:
            self.微信发布器 = 微信自动发布器()
            self.文章处理器 = 文章处理器()
            self.AI配图系统 = AI配图系统()
            print("🚀 套利报告发布器初始化完成")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            self.微信发布器 = None
    
    def 查找今日报告(self) -> str:
        """查找今日的套利分析报告"""
        今日 = date.today().strftime('%Y%m%d')
        
        # 查找今日的报告文件
        报告模式 = os.path.join(self.采集结果目录, f"套利分析报告_{今日}_*.txt")
        报告文件列表 = glob.glob(报告模式)
        
        if 报告文件列表:
            # 返回最新的报告文件
            最新报告 = max(报告文件列表, key=os.path.getmtime)
            print(f"📄 找到今日报告: {os.path.basename(最新报告)}")
            return 最新报告
        else:
            print(f"❌ 未找到今日({今日})的套利分析报告")
            
            # 查找最近的报告
            所有报告模式 = os.path.join(self.采集结果目录, "套利分析报告_*.txt")
            所有报告 = glob.glob(所有报告模式)
            
            if 所有报告:
                最新报告 = max(所有报告, key=os.path.getmtime)
                print(f"💡 找到最新报告: {os.path.basename(最新报告)}")
                return 最新报告
            else:
                print("❌ 未找到任何套利分析报告")
                return None
    
    def 处理报告内容(self, 报告文件路径: str) -> dict:
        """处理报告内容，转换为发布格式"""
        try:
            with open(报告文件路径, 'r', encoding='utf-8') as f:
                原始内容 = f.read()
            
            # 提取报告信息
            报告信息 = self._解析报告信息(原始内容)
            
            # 生成发布标题
            标题 = self._生成发布标题(报告信息)
            
            # 转换内容格式
            发布内容 = self._转换发布格式(原始内容, 报告信息)
            
            # 生成摘要
            摘要 = self._生成摘要(报告信息)
            
            return {
                '标题': 标题,
                '内容': 发布内容,
                '摘要': 摘要,
                '原始文件': 报告文件路径,
                '报告信息': 报告信息
            }
            
        except Exception as e:
            print(f"❌ 处理报告内容失败: {e}")
            return None
    
    def _解析报告信息(self, 内容: str) -> dict:
        """解析报告基本信息"""
        信息 = {
            '生成时间': '',
            '分析文章数': 0,
            '新股数量': 0,
            'REIT数量': 0,
            '转债数量': 0,
            '套利机会数': 0
        }
        
        # 提取生成时间
        时间匹配 = re.search(r'📅 生成时间: (.+)', 内容)
        if 时间匹配:
            信息['生成时间'] = 时间匹配.group(1)
        
        # 提取分析文章数
        文章数匹配 = re.search(r'📄 分析文章数: (\d+)', 内容)
        if 文章数匹配:
            信息['分析文章数'] = int(文章数匹配.group(1))
        
        # 统计各类信息数量
        信息['新股数量'] = len(re.findall(r'🆕 新股信息:', 内容))
        信息['REIT数量'] = len(re.findall(r'🏢 REIT信息:', 内容))
        信息['转债数量'] = len(re.findall(r'💰 转债信息:', 内容))
        信息['套利机会数'] = len(re.findall(r'💡 套利机会:', 内容))
        
        return 信息
    
    def _生成发布标题(self, 报告信息: dict) -> str:
        """生成发布标题"""
        今日 = datetime.now().strftime('%m月%d日')
        
        # 根据内容生成个性化标题
        if 报告信息['新股数量'] > 0:
            标题 = f"📊 {今日}套利分析：新股打新机会解析"
        elif 报告信息['REIT数量'] > 0:
            标题 = f"🏢 {今日}套利分析：REIT投资机会"
        elif 报告信息['转债数量'] > 0:
            标题 = f"💰 {今日}套利分析：可转债投资策略"
        else:
            标题 = f"📈 {今日}每日套利分析报告"
        
        return 标题
    
    def _转换发布格式(self, 原始内容: str, 报告信息: dict) -> str:
        """转换为适合公众号发布的格式"""
        
        # 添加开头
        发布内容 = f"""🎯 **今日套利分析速览**

📅 **分析时间**: {报告信息.get('生成时间', '未知')}
📊 **数据来源**: {报告信息.get('分析文章数', 0)}篇优质投资文章
🔍 **分析维度**: 新股打新、REIT投资、可转债套利

---

"""
        
        # 处理原始内容
        lines = 原始内容.split('\n')
        当前章节 = ""
        
        for line in lines:
            line = line.strip()
            
            if not line or line.startswith('=') or line.startswith('-'):
                continue
            
            # 处理章节标题
            if line.startswith('📰 文章'):
                当前章节 = "文章分析"
                发布内容 += f"\n## {line}\n"
            elif line.startswith('🆕 新股信息'):
                当前章节 = "新股"
                发布内容 += f"\n### 🆕 **新股打新机会**\n"
            elif line.startswith('🏢 REIT信息'):
                当前章节 = "REIT"
                发布内容 += f"\n### 🏢 **REIT投资机会**\n"
            elif line.startswith('💰 转债信息'):
                当前章节 = "转债"
                发布内容 += f"\n### 💰 **可转债套利**\n"
            elif line.startswith('💡 套利机会'):
                当前章节 = "套利"
                发布内容 += f"\n### 💡 **套利机会分析**\n"
            elif line.startswith('📋 操作建议'):
                当前章节 = "建议"
                发布内容 += f"\n### 📋 **操作建议**\n"
            elif line.startswith('⏰ 重要时间'):
                当前章节 = "时间"
                发布内容 += f"\n### ⏰ **重要时间节点**\n"
            elif line.startswith('🕒 发布时间'):
                发布内容 += f"**发布时间**: {line.split(':', 1)[1].strip()}\n"
            elif line.startswith('   •'):
                # 处理列表项
                内容 = line[4:].strip()
                if len(内容) > 100:
                    内容 = 内容[:100] + "..."
                发布内容 += f"• {内容}\n"
            elif line and not line.startswith('📊') and not line.startswith('📄'):
                # 处理普通内容
                发布内容 += f"{line}\n"
        
        # 添加结尾
        发布内容 += f"""

---

⚠️ **风险提示**
本分析仅供参考，投资有风险，入市需谨慎。请根据自身风险承受能力谨慎投资。

🤖 **数据来源**
本报告基于公开投资文章的智能分析，数据仅供参考。

📱 **获取更多**
关注我们获取每日最新套利分析！

#投资分析 #套利机会 #新股打新 #REIT投资 #可转债
"""
        
        return 发布内容
    
    def _生成摘要(self, 报告信息: dict) -> str:
        """生成文章摘要"""
        摘要部分 = []
        
        if 报告信息['新股数量'] > 0:
            摘要部分.append("新股打新机会")
        if 报告信息['REIT数量'] > 0:
            摘要部分.append("REIT投资分析")
        if 报告信息['转债数量'] > 0:
            摘要部分.append("可转债套利")
        
        if 摘要部分:
            摘要 = f"今日套利分析：{' | '.join(摘要部分)}。基于{报告信息['分析文章数']}篇优质文章的深度分析。"
        else:
            摘要 = f"今日套利分析报告，基于{报告信息['分析文章数']}篇投资文章的智能分析。"
        
        return 摘要
    
    def 自动发布到草稿(self, 报告文件路径: str = None) -> bool:
        """自动发布套利报告到公众号草稿"""
        try:
            if not self.微信发布器:
                print("❌ 微信发布器未初始化")
                return False
            
            # 查找报告文件
            if not 报告文件路径:
                报告文件路径 = self.查找今日报告()
            
            if not 报告文件路径:
                print("❌ 未找到要发布的报告文件")
                return False
            
            print(f"📄 准备发布报告: {os.path.basename(报告文件路径)}")
            
            # 处理报告内容
            发布数据 = self.处理报告内容(报告文件路径)
            
            if not 发布数据:
                print("❌ 处理报告内容失败")
                return False
            
            print(f"📝 标题: {发布数据['标题']}")
            print(f"📊 摘要: {发布数据['摘要'][:100]}...")
            
            # 生成配图
            print("🎨 正在生成配图...")
            配图路径 = self.AI配图系统.生成配图("投资分析套利报告", "商务")
            
            if not 配图路径:
                print("⚠️ 配图生成失败，将使用默认配图")
            
            # 发布到草稿
            print("📢 正在发布到公众号草稿...")
            
            发布结果 = self.微信发布器.发布文章(
                标题=发布数据['标题'],
                内容=发布数据['内容'],
                摘要=发布数据['摘要'],
                封面图片=配图路径,
                发布类型='草稿'  # 发布到草稿而不是直接发布
            )
            
            if 发布结果:
                print("🎉 套利报告已成功发布到公众号草稿！")
                print("💡 请登录公众号后台查看草稿并进行最终发布")
                return True
            else:
                print("❌ 发布到草稿失败")
                return False
                
        except Exception as e:
            print(f"❌ 自动发布过程异常: {e}")
            return False
    
    def 批量发布历史报告(self, 天数: int = 7) -> int:
        """批量发布最近几天的历史报告"""
        try:
            # 查找最近几天的报告
            所有报告 = glob.glob(os.path.join(self.采集结果目录, "套利分析报告_*.txt"))
            
            if not 所有报告:
                print("❌ 未找到任何套利分析报告")
                return 0
            
            # 按时间排序，取最近的几个
            所有报告.sort(key=os.path.getmtime, reverse=True)
            最近报告 = 所有报告[:天数]
            
            print(f"📊 找到 {len(最近报告)} 份最近的报告，准备批量发布...")
            
            成功数量 = 0
            
            for i, 报告路径 in enumerate(最近报告, 1):
                print(f"\n📄 处理第 {i}/{len(最近报告)} 份报告...")
                
                if self.自动发布到草稿(报告路径):
                    成功数量 += 1
                    print(f"✅ 第 {i} 份报告发布成功")
                else:
                    print(f"❌ 第 {i} 份报告发布失败")
                
                # 避免频繁请求
                if i < len(最近报告):
                    print("⏳ 等待3秒后处理下一份...")
                    import time
                    time.sleep(3)
            
            print(f"\n📊 批量发布完成：成功 {成功数量}/{len(最近报告)} 份")
            return 成功数量
            
        except Exception as e:
            print(f"❌ 批量发布异常: {e}")
            return 0


def 主函数():
    """主函数"""
    print("📢 套利报告自动发布器")
    print("=" * 50)
    
    # 创建发布器
    发布器 = 套利报告发布器()
    
    if not 发布器.微信发布器:
        print("❌ 微信发布系统未正确初始化")
        print("💡 请确保微信公众号自动发布系统已正确配置")
        return
    
    while True:
        print("\n请选择操作:")
        print("1. 发布今日套利报告")
        print("2. 发布指定报告文件")
        print("3. 批量发布最近报告")
        print("4. 查看可用报告列表")
        print("5. 退出")
        
        选择 = input("\n请输入选择 (1-5): ").strip()
        
        if 选择 == "1":
            print("\n🚀 开始发布今日套利报告...")
            成功 = 发布器.自动发布到草稿()
            
            if 成功:
                print("✅ 发布成功！请登录公众号后台查看草稿")
            else:
                print("❌ 发布失败，请检查配置和网络")
        
        elif 选择 == "2":
            报告路径 = input("请输入报告文件路径: ").strip()
            
            if os.path.exists(报告路径):
                成功 = 发布器.自动发布到草稿(报告路径)
                
                if 成功:
                    print("✅ 发布成功！")
                else:
                    print("❌ 发布失败")
            else:
                print("❌ 文件不存在")
        
        elif 选择 == "3":
            天数 = input("请输入要发布的报告天数 (默认7天): ").strip()
            
            try:
                天数 = int(天数) if 天数 else 7
                成功数量 = 发布器.批量发布历史报告(天数)
                print(f"📊 批量发布完成，成功发布 {成功数量} 份报告")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif 选择 == "4":
            # 显示可用报告列表
            所有报告 = glob.glob(os.path.join(发布器.采集结果目录, "套利分析报告_*.txt"))
            
            if 所有报告:
                print(f"\n📄 找到 {len(所有报告)} 份套利分析报告:")
                所有报告.sort(key=os.path.getmtime, reverse=True)
                
                for i, 报告路径 in enumerate(所有报告[:10], 1):  # 只显示最近10份
                    文件名 = os.path.basename(报告路径)
                    修改时间 = datetime.fromtimestamp(os.path.getmtime(报告路径))
                    print(f"   {i}. {文件名} ({修改时间.strftime('%Y-%m-%d %H:%M')})")
                
                if len(所有报告) > 10:
                    print(f"   ... 还有 {len(所有报告) - 10} 份报告")
            else:
                print("❌ 未找到任何套利分析报告")
        
        elif 选择 == "5":
            print("👋 感谢使用套利报告发布器！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    主函数()
