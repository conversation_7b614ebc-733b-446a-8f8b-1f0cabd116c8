#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
套利信息提取配置管理器
用于加载和管理套利信息提取规则配置

作者: AI助手
日期: 2025-07-29
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from datetime import datetime

class 套利配置管理器:
    """套利信息提取配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.配置文件路径 = os.path.join(os.path.dirname(__file__), '套利信息提取规则.yaml')
        self.配置数据 = None
        self.加载配置()
    
    def 加载配置(self) -> bool:
        """加载配置文件"""
        try:
            if not os.path.exists(self.配置文件路径):
                print(f"❌ 配置文件不存在: {self.配置文件路径}")
                return False
                
            with open(self.配置文件路径, 'r', encoding='utf-8') as f:
                self.配置数据 = yaml.safe_load(f)
                
            print("✅ 套利信息提取配置加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def 获取基本设置(self) -> Dict[str, Any]:
        """获取基本设置"""
        return self.配置数据.get('基本设置', {})
    
    def 获取文章生成限制(self) -> Dict[str, Any]:
        """获取文章生成限制规则"""
        return self.配置数据.get('文章生成限制', {})
    
    def 获取字数限制(self) -> Dict[str, int]:
        """获取字数限制配置"""
        限制配置 = self.获取文章生成限制()
        return 限制配置.get('字数控制', {
            '最小字数': 800,
            '最大字数': 2500,
            '警告阈值': 2000
        })
    
    def 获取套利信息提取规则(self) -> Dict[str, Any]:
        """获取套利信息提取规则"""
        return self.配置数据.get('套利信息提取', {})
    
    def 获取核心提取内容(self) -> List[str]:
        """获取核心提取内容列表"""
        提取规则 = self.获取套利信息提取规则()
        return 提取规则.get('核心内容', [])
    
    def 获取重点关注配置(self) -> Dict[str, List[str]]:
        """获取重点关注配置"""
        提取规则 = self.获取套利信息提取规则()
        return 提取规则.get('重点关注', {})
    
    def 获取报告结构配置(self) -> Dict[str, Any]:
        """获取报告结构配置"""
        return self.配置数据.get('报告结构', {})
    
    def 获取标准格式(self) -> List[Dict[str, str]]:
        """获取标准报告格式"""
        结构配置 = self.获取报告结构配置()
        return 结构配置.get('标准格式', [])
    
    def 获取内容要求(self) -> Dict[str, Dict[str, Any]]:
        """获取各部分内容要求"""
        结构配置 = self.获取报告结构配置()
        return 结构配置.get('内容要求', {})
    
    def 获取质量控制规则(self) -> Dict[str, Any]:
        """获取质量控制规则"""
        return self.配置数据.get('质量控制', {})
    
    def 获取风险提示规则(self) -> Dict[str, Any]:
        """获取风险提示规则"""
        return self.配置数据.get('风险提示', {})
    
    def 获取标准风险提示(self) -> List[str]:
        """获取标准风险提示列表"""
        风险规则 = self.获取风险提示规则()
        return 风险规则.get('标准风险提示', [])
    
    def 获取禁止内容规则(self) -> Dict[str, Any]:
        """获取禁止内容规则"""
        return self.配置数据.get('禁止内容', {})
    
    def 获取禁止表述(self) -> List[str]:
        """获取禁止表述列表"""
        禁止规则 = self.获取禁止内容规则()
        return 禁止规则.get('禁止表述', [])
    
    def 获取限制词汇(self) -> List[str]:
        """获取限制词汇列表"""
        禁止规则 = self.获取禁止内容规则()
        return 禁止规则.get('限制词汇', [])
    
    def 获取合规要求(self) -> Dict[str, Any]:
        """获取合规要求"""
        return self.配置数据.get('合规要求', {})
    
    def 获取免责声明(self) -> str:
        """获取免责声明内容"""
        合规要求 = self.获取合规要求()
        免责配置 = 合规要求.get('免责声明', {})
        return 免责配置.get('内容', '')
    
    def 获取输出格式配置(self) -> Dict[str, Any]:
        """获取输出格式配置"""
        return self.配置数据.get('输出格式', {})
    
    def 获取文件命名规则(self) -> Dict[str, Any]:
        """获取文件命名规则"""
        输出配置 = self.获取输出格式配置()
        return 输出配置.get('文件命名', {})
    
    def 生成文件名(self, 前缀: str = None) -> str:
        """根据规则生成文件名"""
        命名规则 = self.获取文件命名规则()
        
        if 前缀 is None:
            前缀 = 命名规则.get('前缀', '套利分析报告')
        
        if 命名规则.get('时间戳', True):
            基本设置 = self.获取基本设置()
            时间格式 = 基本设置.get('时间戳格式', '%Y%m%d_%H%M%S')
            时间戳 = datetime.now().strftime(时间格式)
            文件名 = f"{前缀}_{时间戳}"
        else:
            文件名 = 前缀
        
        扩展名 = 命名规则.get('扩展名', '.md')
        return f"{文件名}{扩展名}"
    
    def 获取AI提示词模板(self) -> Dict[str, Any]:
        """获取AI提示词模板"""
        return self.配置数据.get('AI提示词模板', {})
    
    def 获取基础提取提示模板(self) -> str:
        """获取基础提取提示词模板"""
        模板配置 = self.获取AI提示词模板()
        基础提示 = 模板配置.get('基础提取提示', {})
        return 基础提示.get('模板', '')
    
    def 获取总结生成提示模板(self) -> str:
        """获取总结生成提示词模板"""
        模板配置 = self.获取AI提示词模板()
        总结提示 = 模板配置.get('总结生成提示', {})
        return 总结提示.get('模板', '')
    
    def 构建提取提示词(self, 标题: str, 作者: str, 内容: str) -> str:
        """构建完整的提取提示词"""
        模板 = self.获取基础提取提示模板()
        字数限制 = self.获取字数限制()
        禁止表述 = self.获取禁止表述()
        
        return 模板.format(
            标题=标题,
            作者=作者,
            内容=内容,
            最小字数=字数限制.get('最小字数', 800),
            最大字数=字数限制.get('最大字数', 2500),
            禁止表述='、'.join(禁止表述)
        )
    
    def 构建总结提示词(self, 套利信息: str) -> str:
        """构建总结生成提示词"""
        模板 = self.获取总结生成提示模板()
        内容要求 = self.获取内容要求()
        摘要要求 = 内容要求.get('摘要部分', {})
        摘要字数限制 = 摘要要求.get('字数限制', 200)
        
        return 模板.format(
            套利信息=套利信息,
            摘要字数限制=摘要字数限制
        )
    
    def 获取自定义规则(self) -> Dict[str, Any]:
        """获取自定义规则"""
        return self.配置数据.get('自定义规则', {})
    
    def 获取用户自定义配置(self) -> Dict[str, List[str]]:
        """获取用户自定义配置"""
        自定义规则 = self.获取自定义规则()
        return 自定义规则.get('用户自定义', {})
    
    def 获取个性化设置(self) -> Dict[str, str]:
        """获取个性化设置"""
        自定义规则 = self.获取自定义规则()
        return 自定义规则.get('个性化设置', {})
    
    def 验证配置完整性(self) -> bool:
        """验证配置文件完整性"""
        必需配置 = [
            '基本设置', '文章生成限制', '套利信息提取', 
            '报告结构', '质量控制', '风险提示', 
            '禁止内容', '合规要求', '输出格式'
        ]
        
        for 配置项 in 必需配置:
            if 配置项 not in self.配置数据:
                print(f"❌ 缺少必需配置项: {配置项}")
                return False
        
        print("✅ 配置文件完整性验证通过")
        return True
    
    def 保存配置(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.配置文件路径, 'w', encoding='utf-8') as f:
                yaml.dump(self.配置数据, f, allow_unicode=True, default_flow_style=False)
            
            print("✅ 配置文件保存成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件保存失败: {e}")
            return False

# 全局配置管理器实例
套利配置 = 套利配置管理器()

# 便捷函数
def 获取配置(配置类型: str = None) -> Any:
    """获取指定类型的配置"""
    if 配置类型 is None:
        return 套利配置.配置数据
    
    配置映射 = {
        '基本设置': 套利配置.获取基本设置,
        '字数限制': 套利配置.获取字数限制,
        '提取规则': 套利配置.获取套利信息提取规则,
        '报告结构': 套利配置.获取报告结构配置,
        '质量控制': 套利配置.获取质量控制规则,
        '风险提示': 套利配置.获取风险提示规则,
        '禁止内容': 套利配置.获取禁止内容规则,
        '合规要求': 套利配置.获取合规要求,
        '输出格式': 套利配置.获取输出格式配置,
        '自定义规则': 套利配置.获取自定义规则
    }
    
    获取函数 = 配置映射.get(配置类型)
    if 获取函数:
        return 获取函数()
    else:
        print(f"❌ 未知的配置类型: {配置类型}")
        return {}

def 生成文件名(前缀: str = None) -> str:
    """生成符合规则的文件名"""
    return 套利配置.生成文件名(前缀)

def 构建提取提示词(标题: str, 作者: str, 内容: str) -> str:
    """构建提取提示词"""
    return 套利配置.构建提取提示词(标题, 作者, 内容)

def 构建总结提示词(套利信息: str) -> str:
    """构建总结提示词"""
    return 套利配置.构建总结提示词(套利信息)

if __name__ == "__main__":
    # 测试配置加载
    print("🧪 测试套利信息提取配置")
    print("=" * 50)
    
    # 验证配置完整性
    if 套利配置.验证配置完整性():
        print("📋 配置项预览:")
        print(f"- 最小字数: {套利配置.获取字数限制()['最小字数']}")
        print(f"- 最大字数: {套利配置.获取字数限制()['最大字数']}")
        print(f"- 核心内容数量: {len(套利配置.获取核心提取内容())}")
        print(f"- 禁止表述数量: {len(套利配置.获取禁止表述())}")
        print(f"- 标准风险提示数量: {len(套利配置.获取标准风险提示())}")
        
        # 测试文件名生成
        测试文件名 = 生成文件名()
        print(f"- 生成的文件名: {测试文件名}")
        
        print("\n✅ 套利信息提取配置测试完成")
    else:
        print("❌ 配置验证失败")
