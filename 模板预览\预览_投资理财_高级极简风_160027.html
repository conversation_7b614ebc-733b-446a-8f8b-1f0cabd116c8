<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板预览 - 投资理财 高级极简风</title>
    <style>
        body { margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #eee; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>模板预览</h1>
            <p>主题: 投资理财 | 样式: 高级极简风 | 生成时间: 2025-07-29 16:00:27</p>
        </div>
        
<div style="font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;">
    <h1 style="color: #2c3e50; font-size: 24px; text-align: center; margin: 20px 0; font-weight: 300; letter-spacing: 1px;">这是一个示例标题</h1>
    
    <div style="background: #ecf0f1; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db;">
        <p style="margin: 0; font-size: 16px;">📝 这是二级标题 这是一段示例内容，用来展示模板的效果。这是三级标题 - 这是列表项1 - 这是列表项2 这是强调文本，这是普通文本。</p>
    </div>
    
    <h2 style="color: #2c3e50; font-size: 20px; margin: 25px 0 15px 0; font-weight: 500;">这是二级标题</h2>
<p style="margin: 15px 0; font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;">这是一段示例内容，用来展示模板的效果。</p>
<h3 style="color: #3498db; font-size: 18px; margin: 20px 0 10px 0;">这是三级标题</h3>
<p style="margin: 15px 0; font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;">- 这是列表项1</p>
<p style="margin: 15px 0; font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;">- 这是列表项2</p>
<p style="margin: 15px 0; font-family: 'Microsoft YaHei', sans-serif; color: #2c3e50; line-height: 1.7;"><strong>这是强调文本</strong>，这是普通文本。</p>
    
    <div style="text-align: center; margin: 30px 0; padding: 15px; border-top: 1px solid #eee;">
        <p style="color: #999; margin: 0; font-size: 14px;">感谢阅读 | 欢迎分享</p>
    </div>
</div>
                
    </div>
</body>
</html>